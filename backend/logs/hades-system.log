2025-07-28 15:22:22 [main] INFO  com.hades.system.HadesSystemApplication - Starting HadesSystemApplication using Java 21.0.7 on NoahdeMacBook-Air.local with PID 91430 (/Users/<USER>/codebase/work/hades-system/backend/target/classes started by noah in /Users/<USER>/codebase/work/hades-system/backend)
2025-07-28 15:22:22 [main] DEBUG com.hades.system.HadesSystemApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-07-28 15:22:22 [main] INFO  com.hades.system.HadesSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-28 15:22:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 15:22:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 15:22:22 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-07-28 15:22:23 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-28 15:22:23 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-28 15:22:23 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-28 15:22:23 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-28 15:22:23 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 441 ms
2025-07-28 15:22:23 [main] DEBUG com.hades.system.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-28 15:22:23 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-28 15:22:23 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-28 15:22:23 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: 1d00f2f0-0e3c-4f1c-9b5c-2002f26ed46d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-28 15:22:23 [main] INFO  o.s.security.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4fa0ee7e, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@202f054f, org.springframework.security.web.context.SecurityContextPersistenceFilter@402d6012, org.springframework.security.web.header.HeaderWriterFilter@49c83262, org.springframework.web.filter.CorsFilter@64da6cbd, org.springframework.security.web.authentication.logout.LogoutFilter@7df4709e, com.hades.system.security.JwtAuthenticationFilter@1b9d9a2b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@41d20f06, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69f9b561, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@141bb6b8, org.springframework.security.web.session.SessionManagementFilter@247bbfba, org.springframework.security.web.access.ExceptionTranslationFilter@6be766d1, org.springframework.security.web.access.intercept.AuthorizationFilter@7ab2ba47]
2025-07-28 15:22:23 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/api'
2025-07-28 15:22:23 [main] INFO  com.hades.system.HadesSystemApplication - Started HadesSystemApplication in 1.293 seconds (JVM running for 1.392)
2025-07-28 15:22:38 [http-nio-8080-exec-2] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-28 15:22:38 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-28 15:22:38 [http-nio-8080-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-07-28 15:22:38 [http-nio-8080-exec-2] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /admin/info
2025-07-28 15:22:38 [http-nio-8080-exec-1] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /admin/info
2025-07-28 15:22:38 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-28 15:22:38 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-28 15:22:38 [http-nio-8080-exec-2] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-28 15:22:38 [http-nio-8080-exec-1] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-28 15:22:38 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /admin/info
2025-07-28 15:22:38 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-28 15:22:38 [http-nio-8080-exec-3] DEBUG com.hades.system.security.JwtAuthenticationFilter - 管理员认证成功: adminId=1, username=admin
2025-07-28 15:22:38 [http-nio-8080-exec-3] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /admin/info
2025-07-28 15:22:38 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /admin/info
2025-07-28 15:22:38 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-28 15:22:38 [http-nio-8080-exec-3] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-28 15:22:38 [http-nio-8080-exec-4] DEBUG com.hades.system.security.JwtAuthenticationFilter - 管理员认证成功: adminId=1, username=admin
2025-07-28 15:22:38 [http-nio-8080-exec-4] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /admin/info
2025-07-28 15:22:38 [http-nio-8080-exec-4] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-28 15:22:38 [http-nio-8080-exec-5] DEBUG org.springframework.security.web.FilterChainProxy - Securing OPTIONS /admin/parking/lots?page=1&size=20&name=&status=
2025-07-28 15:22:38 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-28 15:22:38 [http-nio-8080-exec-5] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Securing GET /admin/parking/lots?page=1&size=20&name=&status=
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Set SecurityContextHolder to empty SecurityContext
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG com.hades.system.security.JwtAuthenticationFilter - 管理员认证成功: adminId=1, username=admin
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG org.springframework.security.web.FilterChainProxy - Secured GET /admin/parking/lots?page=1&size=20&name=&status=
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.hades.system.mapper.ParkingLotMapper.selectPage - ==>  Preparing: SELECT id,name,location,total_spaces,hourly_rate,status,created_at FROM parking_lots ORDER BY created_at DESC
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.hades.system.mapper.ParkingLotMapper.selectPage - ==> Parameters: 
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.hades.system.mapper.ParkingLotMapper.selectPage - <==      Total: 6
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==>  Preparing: SELECT pl.id, pl.name, pl.total_spaces, COUNT(ps.id) as created_spaces, SUM(CASE WHEN ps.status = 'available' THEN 1 ELSE 0 END) as available_spaces, SUM(CASE WHEN ps.status = 'occupied' THEN 1 ELSE 0 END) as occupied_spaces, SUM(CASE WHEN ps.status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_spaces, SUM(CASE WHEN ps.status = 'reserved' THEN 1 ELSE 0 END) as reserved_spaces FROM parking_lots pl LEFT JOIN parking_spaces ps ON pl.id = ps.lot_id WHERE pl.id = ? GROUP BY pl.id
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==> Parameters: 9(Integer)
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - <==      Total: 1
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==>  Preparing: SELECT pl.id, pl.name, pl.total_spaces, COUNT(ps.id) as created_spaces, SUM(CASE WHEN ps.status = 'available' THEN 1 ELSE 0 END) as available_spaces, SUM(CASE WHEN ps.status = 'occupied' THEN 1 ELSE 0 END) as occupied_spaces, SUM(CASE WHEN ps.status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_spaces, SUM(CASE WHEN ps.status = 'reserved' THEN 1 ELSE 0 END) as reserved_spaces FROM parking_lots pl LEFT JOIN parking_spaces ps ON pl.id = ps.lot_id WHERE pl.id = ? GROUP BY pl.id
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==> Parameters: 8(Integer)
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - <==      Total: 1
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==>  Preparing: SELECT pl.id, pl.name, pl.total_spaces, COUNT(ps.id) as created_spaces, SUM(CASE WHEN ps.status = 'available' THEN 1 ELSE 0 END) as available_spaces, SUM(CASE WHEN ps.status = 'occupied' THEN 1 ELSE 0 END) as occupied_spaces, SUM(CASE WHEN ps.status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_spaces, SUM(CASE WHEN ps.status = 'reserved' THEN 1 ELSE 0 END) as reserved_spaces FROM parking_lots pl LEFT JOIN parking_spaces ps ON pl.id = ps.lot_id WHERE pl.id = ? GROUP BY pl.id
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==> Parameters: 7(Integer)
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - <==      Total: 1
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==>  Preparing: SELECT pl.id, pl.name, pl.total_spaces, COUNT(ps.id) as created_spaces, SUM(CASE WHEN ps.status = 'available' THEN 1 ELSE 0 END) as available_spaces, SUM(CASE WHEN ps.status = 'occupied' THEN 1 ELSE 0 END) as occupied_spaces, SUM(CASE WHEN ps.status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_spaces, SUM(CASE WHEN ps.status = 'reserved' THEN 1 ELSE 0 END) as reserved_spaces FROM parking_lots pl LEFT JOIN parking_spaces ps ON pl.id = ps.lot_id WHERE pl.id = ? GROUP BY pl.id
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==> Parameters: 6(Integer)
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - <==      Total: 1
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==>  Preparing: SELECT pl.id, pl.name, pl.total_spaces, COUNT(ps.id) as created_spaces, SUM(CASE WHEN ps.status = 'available' THEN 1 ELSE 0 END) as available_spaces, SUM(CASE WHEN ps.status = 'occupied' THEN 1 ELSE 0 END) as occupied_spaces, SUM(CASE WHEN ps.status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_spaces, SUM(CASE WHEN ps.status = 'reserved' THEN 1 ELSE 0 END) as reserved_spaces FROM parking_lots pl LEFT JOIN parking_spaces ps ON pl.id = ps.lot_id WHERE pl.id = ? GROUP BY pl.id
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==> Parameters: 5(Integer)
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - <==      Total: 1
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==>  Preparing: SELECT pl.id, pl.name, pl.total_spaces, COUNT(ps.id) as created_spaces, SUM(CASE WHEN ps.status = 'available' THEN 1 ELSE 0 END) as available_spaces, SUM(CASE WHEN ps.status = 'occupied' THEN 1 ELSE 0 END) as occupied_spaces, SUM(CASE WHEN ps.status = 'maintenance' THEN 1 ELSE 0 END) as maintenance_spaces, SUM(CASE WHEN ps.status = 'reserved' THEN 1 ELSE 0 END) as reserved_spaces FROM parking_lots pl LEFT JOIN parking_spaces ps ON pl.id = ps.lot_id WHERE pl.id = ? GROUP BY pl.id
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - ==> Parameters: 4(Integer)
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG c.h.s.m.ParkingLotMapper.getParkingLotStatistics - <==      Total: 1
2025-07-28 15:22:38 [http-nio-8080-exec-6] DEBUG o.s.s.web.context.SecurityContextPersistenceFilter - Cleared SecurityContextHolder to complete request
2025-07-28 15:56:14 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-28 15:56:14 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-28 15:56:24 [main] INFO  com.hades.system.HadesSystemApplication - Starting HadesSystemApplication using Java 21.0.7 on NoahdeMacBook-Air.local with PID 91721 (/Users/<USER>/codebase/work/hades-system/backend/target/classes started by noah in /Users/<USER>/codebase/work/hades-system/backend)
2025-07-28 15:56:24 [main] DEBUG com.hades.system.HadesSystemApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-07-28 15:56:24 [main] INFO  com.hades.system.HadesSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-28 15:56:24 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 15:56:24 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 15:56:24 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-07-28 15:56:24 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-28 15:56:24 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-28 15:56:24 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-28 15:56:24 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-28 15:56:24 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 431 ms
2025-07-28 15:56:24 [main] DEBUG com.hades.system.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-28 15:56:24 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-28 15:56:25 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-28 15:56:25 [main] ERROR org.mybatis.spring.mapper.MapperFactoryBean - Error while adding the mapper 'interface com.hades.system.mapper.ProxyOrderMapper' to configuration.
org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 689; 元素内容必须由格式正确的字符数据或标记组成。
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:263)
	at org.apache.ibatis.parsing.XPathParser.<init>(XPathParser.java:117)
	at org.apache.ibatis.scripting.xmltags.XMLLanguageDriver.createSqlSource(XMLLanguageDriver.java:51)
	at com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver.createSqlSource(MybatisXMLLanguageDriver.java:61)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.buildSqlSourceFromStrings(MybatisMapperAnnotationBuilder.java:641)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.buildSqlSource(MybatisMapperAnnotationBuilder.java:626)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.lambda$parseStatement$2(MybatisMapperAnnotationBuilder.java:296)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parseStatement(MybatisMapperAnnotationBuilder.java:295)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:113)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:94)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:129)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:642)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:642)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:921)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.hades.system.HadesSystemApplication.main(HadesSystemApplication.java:20)
Caused by: org.xml.sax.SAXParseException: 元素内容必须由格式正确的字符数据或标记组成。
	at java.xml/com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.createSAXParseException(ErrorHandlerWrapper.java:204)
	at java.xml/com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.fatalError(ErrorHandlerWrapper.java:178)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:400)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:327)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLScanner.reportFatalError(XMLScanner.java:1465)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.startOfMarkup(XMLDocumentFragmentScannerImpl.java:2589)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.next(XMLDocumentFragmentScannerImpl.java:2679)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentScannerImpl.next(XMLDocumentScannerImpl.java:605)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl.scanDocument(XMLDocumentFragmentScannerImpl.java:542)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:889)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:825)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XMLParser.parse(XMLParser.java:141)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.DOMParser.parse(DOMParser.java:247)
	at java.xml/com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderImpl.parse(DocumentBuilderImpl.java:342)
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:261)
	... 59 common frames omitted
2025-07-28 15:56:25 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminProxyServiceController': Unsatisfied dependency expressed through field 'proxyService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'proxyServiceImpl': Unsatisfied dependency expressed through field 'proxyOrderMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'proxyOrderMapper' defined in file [/Users/<USER>/codebase/work/hades-system/backend/target/classes/com/hades/system/mapper/ProxyOrderMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 689; 元素内容必须由格式正确的字符数据或标记组成。
2025-07-28 15:56:25 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-28 15:56:25 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-28 15:56:25 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-28 15:56:25 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-28 15:56:25 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminProxyServiceController': Unsatisfied dependency expressed through field 'proxyService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'proxyServiceImpl': Unsatisfied dependency expressed through field 'proxyOrderMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'proxyOrderMapper' defined in file [/Users/<USER>/codebase/work/hades-system/backend/target/classes/com/hades/system/mapper/ProxyOrderMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 689; 元素内容必须由格式正确的字符数据或标记组成。
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:662)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:642)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:921)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.hades.system.HadesSystemApplication.main(HadesSystemApplication.java:20)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'proxyServiceImpl': Unsatisfied dependency expressed through field 'proxyOrderMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'proxyOrderMapper' defined in file [/Users/<USER>/codebase/work/hades-system/backend/target/classes/com/hades/system/mapper/ProxyOrderMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 689; 元素内容必须由格式正确的字符数据或标记组成。
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:662)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:642)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'proxyOrderMapper' defined in file [/Users/<USER>/codebase/work/hades-system/backend/target/classes/com/hades/system/mapper/ProxyOrderMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 689; 元素内容必须由格式正确的字符数据或标记组成。
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	... 34 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 689; 元素内容必须由格式正确的字符数据或标记组成。
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:83)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 44 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 689; 元素内容必须由格式正确的字符数据或标记组成。
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:263)
	at org.apache.ibatis.parsing.XPathParser.<init>(XPathParser.java:117)
	at org.apache.ibatis.scripting.xmltags.XMLLanguageDriver.createSqlSource(XMLLanguageDriver.java:51)
	at com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver.createSqlSource(MybatisXMLLanguageDriver.java:61)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.buildSqlSourceFromStrings(MybatisMapperAnnotationBuilder.java:641)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.buildSqlSource(MybatisMapperAnnotationBuilder.java:626)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.lambda$parseStatement$2(MybatisMapperAnnotationBuilder.java:296)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parseStatement(MybatisMapperAnnotationBuilder.java:295)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:113)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:94)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:129)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	... 47 common frames omitted
Caused by: org.xml.sax.SAXParseException: 元素内容必须由格式正确的字符数据或标记组成。
	at java.xml/com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.createSAXParseException(ErrorHandlerWrapper.java:204)
	at java.xml/com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.fatalError(ErrorHandlerWrapper.java:178)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:400)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:327)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLScanner.reportFatalError(XMLScanner.java:1465)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.startOfMarkup(XMLDocumentFragmentScannerImpl.java:2589)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.next(XMLDocumentFragmentScannerImpl.java:2679)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentScannerImpl.next(XMLDocumentScannerImpl.java:605)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl.scanDocument(XMLDocumentFragmentScannerImpl.java:542)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:889)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:825)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XMLParser.parse(XMLParser.java:141)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.DOMParser.parse(DOMParser.java:247)
	at java.xml/com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderImpl.parse(DocumentBuilderImpl.java:342)
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:261)
	... 59 common frames omitted
2025-07-28 15:57:25 [main] INFO  com.hades.system.HadesSystemApplication - Starting HadesSystemApplication using Java 21.0.7 on NoahdeMacBook-Air.local with PID 91752 (/Users/<USER>/codebase/work/hades-system/backend/target/classes started by noah in /Users/<USER>/codebase/work/hades-system/backend)
2025-07-28 15:57:25 [main] DEBUG com.hades.system.HadesSystemApplication - Running with Spring Boot v2.7.14, Spring v5.3.29
2025-07-28 15:57:25 [main] INFO  com.hades.system.HadesSystemApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-28 15:57:25 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 15:57:25 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 15:57:25 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-07-28 15:57:25 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-07-28 15:57:25 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-28 15:57:25 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.78]
2025-07-28 15:57:25 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-28 15:57:25 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 451 ms
2025-07-28 15:57:25 [main] DEBUG com.hades.system.security.JwtAuthenticationFilter - Filter 'jwtAuthenticationFilter' configured for use
2025-07-28 15:57:25 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-28 15:57:26 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-28 15:57:26 [main] ERROR org.mybatis.spring.mapper.MapperFactoryBean - Error while adding the mapper 'interface com.hades.system.mapper.ProxyOrderMapper' to configuration.
org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 699; 元素内容必须由格式正确的字符数据或标记组成。
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:263)
	at org.apache.ibatis.parsing.XPathParser.<init>(XPathParser.java:117)
	at org.apache.ibatis.scripting.xmltags.XMLLanguageDriver.createSqlSource(XMLLanguageDriver.java:51)
	at com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver.createSqlSource(MybatisXMLLanguageDriver.java:61)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.buildSqlSourceFromStrings(MybatisMapperAnnotationBuilder.java:641)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.buildSqlSource(MybatisMapperAnnotationBuilder.java:626)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.lambda$parseStatement$2(MybatisMapperAnnotationBuilder.java:296)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parseStatement(MybatisMapperAnnotationBuilder.java:295)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:113)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:94)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:129)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:642)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:642)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:921)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.hades.system.HadesSystemApplication.main(HadesSystemApplication.java:20)
Caused by: org.xml.sax.SAXParseException: 元素内容必须由格式正确的字符数据或标记组成。
	at java.xml/com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.createSAXParseException(ErrorHandlerWrapper.java:204)
	at java.xml/com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.fatalError(ErrorHandlerWrapper.java:178)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:400)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:327)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLScanner.reportFatalError(XMLScanner.java:1465)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.startOfMarkup(XMLDocumentFragmentScannerImpl.java:2589)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.next(XMLDocumentFragmentScannerImpl.java:2679)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentScannerImpl.next(XMLDocumentScannerImpl.java:605)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl.scanDocument(XMLDocumentFragmentScannerImpl.java:542)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:889)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:825)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XMLParser.parse(XMLParser.java:141)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.DOMParser.parse(DOMParser.java:247)
	at java.xml/com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderImpl.parse(DocumentBuilderImpl.java:342)
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:261)
	... 59 common frames omitted
2025-07-28 15:57:26 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminProxyServiceController': Unsatisfied dependency expressed through field 'proxyService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'proxyServiceImpl': Unsatisfied dependency expressed through field 'proxyOrderMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'proxyOrderMapper' defined in file [/Users/<USER>/codebase/work/hades-system/backend/target/classes/com/hades/system/mapper/ProxyOrderMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 699; 元素内容必须由格式正确的字符数据或标记组成。
2025-07-28 15:57:26 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-28 15:57:26 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-28 15:57:26 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-07-28 15:57:26 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-28 15:57:26 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminProxyServiceController': Unsatisfied dependency expressed through field 'proxyService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'proxyServiceImpl': Unsatisfied dependency expressed through field 'proxyOrderMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'proxyOrderMapper' defined in file [/Users/<USER>/codebase/work/hades-system/backend/target/classes/com/hades/system/mapper/ProxyOrderMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 699; 元素内容必须由格式正确的字符数据或标记组成。
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:662)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:642)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:921)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:731)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:408)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1303)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1292)
	at com.hades.system.HadesSystemApplication.main(HadesSystemApplication.java:20)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'proxyServiceImpl': Unsatisfied dependency expressed through field 'proxyOrderMapper'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'proxyOrderMapper' defined in file [/Users/<USER>/codebase/work/hades-system/backend/target/classes/com/hades/system/mapper/ProxyOrderMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 699; 元素内容必须由格式正确的字符数据或标记组成。
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:662)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:642)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1431)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	... 20 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'proxyOrderMapper' defined in file [/Users/<USER>/codebase/work/hades-system/backend/target/classes/com/hades/system/mapper/ProxyOrderMapper.class]: Invocation of init method failed; nested exception is java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 699; 元素内容必须由格式正确的字符数据或标记组成。
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1804)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:620)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:659)
	... 34 common frames omitted
Caused by: java.lang.IllegalArgumentException: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 699; 元素内容必须由格式正确的字符数据或标记组成。
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:83)
	at org.springframework.dao.support.DaoSupport.afterPropertiesSet(DaoSupport.java:44)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1863)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1800)
	... 44 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error creating document instance.  Cause: org.xml.sax.SAXParseException; lineNumber: 1; columnNumber: 699; 元素内容必须由格式正确的字符数据或标记组成。
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:263)
	at org.apache.ibatis.parsing.XPathParser.<init>(XPathParser.java:117)
	at org.apache.ibatis.scripting.xmltags.XMLLanguageDriver.createSqlSource(XMLLanguageDriver.java:51)
	at com.baomidou.mybatisplus.core.MybatisXMLLanguageDriver.createSqlSource(MybatisXMLLanguageDriver.java:61)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.buildSqlSourceFromStrings(MybatisMapperAnnotationBuilder.java:641)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.buildSqlSource(MybatisMapperAnnotationBuilder.java:626)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.lambda$parseStatement$2(MybatisMapperAnnotationBuilder.java:296)
	at java.base/java.util.Optional.ifPresent(Optional.java:178)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parseStatement(MybatisMapperAnnotationBuilder.java:295)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:113)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:94)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:129)
	at org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig(MapperFactoryBean.java:80)
	... 47 common frames omitted
Caused by: org.xml.sax.SAXParseException: 元素内容必须由格式正确的字符数据或标记组成。
	at java.xml/com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.createSAXParseException(ErrorHandlerWrapper.java:204)
	at java.xml/com.sun.org.apache.xerces.internal.util.ErrorHandlerWrapper.fatalError(ErrorHandlerWrapper.java:178)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:400)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLErrorReporter.reportError(XMLErrorReporter.java:327)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLScanner.reportFatalError(XMLScanner.java:1465)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.startOfMarkup(XMLDocumentFragmentScannerImpl.java:2589)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.next(XMLDocumentFragmentScannerImpl.java:2679)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentScannerImpl.next(XMLDocumentScannerImpl.java:605)
	at java.xml/com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl.scanDocument(XMLDocumentFragmentScannerImpl.java:542)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:889)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XML11Configuration.parse(XML11Configuration.java:825)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.XMLParser.parse(XMLParser.java:141)
	at java.xml/com.sun.org.apache.xerces.internal.parsers.DOMParser.parse(DOMParser.java:247)
	at java.xml/com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderImpl.parse(DocumentBuilderImpl.java:342)
	at org.apache.ibatis.parsing.XPathParser.createDocument(XPathParser.java:261)
	... 59 common frames omitted
