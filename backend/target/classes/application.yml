# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api

# Spring配置
spring:
  application:
    name: hades-system
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************
    username: root
    password: mysql@2025
    druid:
      # 初始连接数
      initial-size: 10
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,*************
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    connection-timeout: 15000
    publisher-confirms: true
    publisher-returns: true
    template:
      mandatory: true

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.hades.system.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.hades.system: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/hades-system.log

# 自定义配置
hades:
  # JWT配置
  jwt:
    secret: hadesSystemJwtSecretKey2024ForHadesSystemSecureAuthentication
    expiration: 604800 # 7天，单位秒
    header: Authorization
    prefix: Bearer
  
  # 微信小程序配置
  wechat:
    appid: test_appid_123
    secret: test_secret_456
    
  # 微信支付配置
  wechat-pay:
    mch-id: your_mch_id
    api-key: your_api_key
    cert-path: classpath:cert/apiclient_cert.p12
    
  # 腾讯云COS配置
  cos:
    secret-id: your_secret_id
    secret-key: your_secret_key
    region: ap-guangzhou
    bucket-name: hades-system
    
  # 短信配置
  sms:
    secret-id: your_sms_secret_id
    secret-key: your_sms_secret_key
    sdk-app-id: your_sdk_app_id
    template-id: your_template_id
    sign-name: 哈迪斯殡葬服务

  # 文件上传配置
  upload:
    path: /tmp/uploads
    url-prefix: http://localhost:8080/api/file
    max-size: 10485760  # 10MB
