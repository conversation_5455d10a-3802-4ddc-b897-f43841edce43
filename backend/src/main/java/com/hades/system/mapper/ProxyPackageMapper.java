package com.hades.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hades.system.entity.ProxyPackage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 代客服务套餐Mapper接口
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Mapper
public interface ProxyPackageMapper extends BaseMapper<ProxyPackage> {
    
    /**
     * 获取活跃的套餐列表
     */
    @Select("SELECT * FROM proxy_packages WHERE status = 'active' ORDER BY sort_order ASC")
    List<ProxyPackage> selectActivePackages();
}
