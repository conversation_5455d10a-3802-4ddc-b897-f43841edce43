package com.hades.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hades.system.entity.ProxyServiceReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 代客服务报告Mapper接口
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Mapper
public interface ProxyServiceReportMapper extends BaseMapper<ProxyServiceReport> {
    
    /**
     * 根据订单ID查询服务报告
     */
    @Select("SELECT psr.*, po.order_no, ps.name as staff_name " +
            "FROM proxy_service_reports psr " +
            "LEFT JOIN proxy_orders po ON psr.order_id = po.id " +
            "LEFT JOIN proxy_staff ps ON psr.staff_id = ps.id " +
            "WHERE psr.order_id = #{orderId}")
    ProxyServiceReport selectByOrderId(@Param("orderId") Integer orderId);
}
