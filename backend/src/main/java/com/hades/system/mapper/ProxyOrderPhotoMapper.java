package com.hades.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hades.system.entity.ProxyOrderPhoto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 代客服务照片Mapper接口
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Mapper
public interface ProxyOrderPhotoMapper extends BaseMapper<ProxyOrderPhoto> {
    
    /**
     * 根据订单ID查询照片列表
     */
    @Select("SELECT * FROM proxy_order_photos WHERE order_id = #{orderId} ORDER BY sort_order ASC, created_at ASC")
    List<ProxyOrderPhoto> selectByOrderId(@Param("orderId") Integer orderId);
    
    /**
     * 根据报告ID查询照片列表
     */
    @Select("SELECT * FROM proxy_order_photos WHERE report_id = #{reportId} ORDER BY sort_order ASC, created_at ASC")
    List<ProxyOrderPhoto> selectByReportId(@Param("reportId") Integer reportId);
}
