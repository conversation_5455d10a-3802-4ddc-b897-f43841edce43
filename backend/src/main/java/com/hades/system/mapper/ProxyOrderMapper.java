package com.hades.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hades.system.entity.ProxyOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 代客订单Mapper接口
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Mapper
public interface ProxyOrderMapper extends BaseMapper<ProxyOrder> {
    
    /**
     * 分页查询订单列表（带关联信息）
     */
    @Select("SELECT po.*, u.nickname as user_name, pp.name as package_name, " +
            "ps.name as staff_name, c.name as cemetery_name, gs.site_number as grave_site_number " +
            "FROM proxy_orders po " +
            "LEFT JOIN users u ON po.user_id = u.id " +
            "LEFT JOIN proxy_packages pp ON po.package_id = pp.id " +
            "LEFT JOIN proxy_staff ps ON po.staff_id = ps.id " +
            "LEFT JOIN cemeteries c ON po.cemetery_id = c.id " +
            "LEFT JOIN grave_sites gs ON po.grave_site_id = gs.id " +
            "ORDER BY po.created_at DESC")
    Page<ProxyOrder> selectOrderPageWithDetails(Page<ProxyOrder> page,
                                               @Param("status") String status,
                                               @Param("staffId") Integer staffId,
                                               @Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);
    
    /**
     * 根据用户ID查询订单列表
     */
    @Select("SELECT po.*, pp.name as package_name, ps.name as staff_name " +
            "FROM proxy_orders po " +
            "LEFT JOIN proxy_packages pp ON po.package_id = pp.id " +
            "LEFT JOIN proxy_staff ps ON po.staff_id = ps.id " +
            "WHERE po.user_id = #{userId} " +
            "ORDER BY po.created_at DESC")
    List<ProxyOrder> selectOrdersByUserId(@Param("userId") Integer userId);
    
    /**
     * 获取订单统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_orders, " +
            "COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders, " +
            "COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed_orders, " +
            "COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_orders, " +
            "COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders, " +
            "SUM(total_amount) as total_amount, " +
            "SUM(paid_amount) as paid_amount " +
            "FROM proxy_orders " +
            "WHERE created_at >= #{startDate} AND created_at <= #{endDate}")
    Map<String, Object> getOrderStatistics(@Param("startDate") LocalDate startDate, 
                                          @Param("endDate") LocalDate endDate);
}
