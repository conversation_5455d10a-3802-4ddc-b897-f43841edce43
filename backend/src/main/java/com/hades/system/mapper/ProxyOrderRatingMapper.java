package com.hades.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hades.system.entity.ProxyOrderRating;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代客服务评价Mapper接口
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Mapper
public interface ProxyOrderRatingMapper extends BaseMapper<ProxyOrderRating> {
    
    /**
     * 根据订单ID查询评价
     */
    @Select("SELECT por.*, po.order_no, u.nickname as user_name, ps.name as staff_name " +
            "FROM proxy_order_ratings por " +
            "LEFT JOIN proxy_orders po ON por.order_id = po.id " +
            "LEFT JOIN users u ON por.user_id = u.id " +
            "LEFT JOIN proxy_staff ps ON por.staff_id = ps.id " +
            "WHERE por.order_id = #{orderId}")
    ProxyOrderRating selectByOrderId(@Param("orderId") Integer orderId);
    
    /**
     * 根据服务人员ID查询评价列表
     */
    @Select("SELECT por.*, po.order_no, u.nickname as user_name " +
            "FROM proxy_order_ratings por " +
            "LEFT JOIN proxy_orders po ON por.order_id = po.id " +
            "LEFT JOIN users u ON por.user_id = u.id " +
            "WHERE por.staff_id = #{staffId} " +
            "ORDER BY por.created_at DESC")
    List<ProxyOrderRating> selectByStaffId(@Param("staffId") Integer staffId);
    
    /**
     * 计算服务人员平均评分
     */
    @Select("SELECT AVG(overall_rating) FROM proxy_order_ratings WHERE staff_id = #{staffId}")
    BigDecimal calculateStaffAverageRating(@Param("staffId") Integer staffId);
}
