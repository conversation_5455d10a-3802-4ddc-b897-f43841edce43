package com.hades.system.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hades.system.common.Result;
import com.hades.system.entity.*;
import com.hades.system.service.ProxyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 管理后台代客服务控制器
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@RestController
@RequestMapping("/admin/proxy")
public class AdminProxyServiceController {
    
    @Autowired
    private ProxyService proxyService;
    
    // ==================== 套餐管理 ====================
    
    /**
     * 获取套餐分页列表
     */
    @GetMapping("/packages")
    public Result<Page<ProxyPackage>> getPackageList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status) {
        try {
            Page<ProxyPackage> result = proxyService.getPackagePage(page, size, name, status);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failed("获取套餐列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取套餐详情
     */
    @GetMapping("/packages/{id}")
    public Result<ProxyPackage> getPackageDetail(@PathVariable Integer id) {
        try {
            ProxyPackage proxyPackage = proxyService.getPackageById(id);
            if (proxyPackage == null) {
                return Result.failed("套餐不存在");
            }
            return Result.success(proxyPackage);
        } catch (Exception e) {
            return Result.failed("获取套餐详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建套餐
     */
    @PostMapping("/packages")
    public Result<String> createPackage(@Valid @RequestBody ProxyPackage proxyPackage) {
        try {
            boolean success = proxyService.createPackage(proxyPackage);
            if (success) {
                return Result.success("套餐创建成功");
            } else {
                return Result.failed("套餐创建失败");
            }
        } catch (Exception e) {
            return Result.failed("创建套餐失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新套餐
     */
    @PutMapping("/packages/{id}")
    public Result<String> updatePackage(@PathVariable Integer id, 
                                       @Valid @RequestBody ProxyPackage proxyPackage) {
        try {
            proxyPackage.setId(id);
            boolean success = proxyService.updatePackage(proxyPackage);
            if (success) {
                return Result.success("套餐更新成功");
            } else {
                return Result.failed("套餐更新失败");
            }
        } catch (Exception e) {
            return Result.failed("更新套餐失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除套餐
     */
    @DeleteMapping("/packages/{id}")
    public Result<String> deletePackage(@PathVariable Integer id) {
        try {
            boolean success = proxyService.deletePackage(id);
            if (success) {
                return Result.success("套餐删除成功");
            } else {
                return Result.failed("套餐删除失败");
            }
        } catch (Exception e) {
            return Result.failed("删除套餐失败: " + e.getMessage());
        }
    }
    
    // ==================== 服务人员管理 ====================
    
    /**
     * 获取服务人员分页列表
     */
    @GetMapping("/staff")
    public Result<Page<ProxyStaff>> getStaffList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status) {
        try {
            Page<ProxyStaff> result = proxyService.getStaffPage(page, size, name, status);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failed("获取服务人员列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取活跃服务人员列表
     */
    @GetMapping("/staff/active")
    public Result<List<ProxyStaff>> getActiveStaff() {
        try {
            List<ProxyStaff> staff = proxyService.getActiveStaff();
            return Result.success(staff);
        } catch (Exception e) {
            return Result.failed("获取服务人员列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务人员详情
     */
    @GetMapping("/staff/{id}")
    public Result<ProxyStaff> getStaffDetail(@PathVariable Integer id) {
        try {
            ProxyStaff staff = proxyService.getStaffById(id);
            if (staff == null) {
                return Result.failed("服务人员不存在");
            }
            return Result.success(staff);
        } catch (Exception e) {
            return Result.failed("获取服务人员详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建服务人员
     */
    @PostMapping("/staff")
    public Result<String> createStaff(@Valid @RequestBody ProxyStaff proxyStaff) {
        try {
            boolean success = proxyService.createStaff(proxyStaff);
            if (success) {
                return Result.success("服务人员创建成功");
            } else {
                return Result.failed("服务人员创建失败");
            }
        } catch (Exception e) {
            return Result.failed("创建服务人员失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新服务人员
     */
    @PutMapping("/staff/{id}")
    public Result<String> updateStaff(@PathVariable Integer id, 
                                     @Valid @RequestBody ProxyStaff proxyStaff) {
        try {
            proxyStaff.setId(id);
            boolean success = proxyService.updateStaff(proxyStaff);
            if (success) {
                return Result.success("服务人员更新成功");
            } else {
                return Result.failed("服务人员更新失败");
            }
        } catch (Exception e) {
            return Result.failed("更新服务人员失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除服务人员
     */
    @DeleteMapping("/staff/{id}")
    public Result<String> deleteStaff(@PathVariable Integer id) {
        try {
            boolean success = proxyService.deleteStaff(id);
            if (success) {
                return Result.success("服务人员删除成功");
            } else {
                return Result.failed("服务人员删除失败");
            }
        } catch (Exception e) {
            return Result.failed("删除服务人员失败: " + e.getMessage());
        }
    }
    
    // ==================== 订单管理 ====================
    
    /**
     * 获取订单分页列表
     */
    @GetMapping("/orders")
    public Result<Page<ProxyOrder>> getOrderList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Integer staffId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            Page<ProxyOrder> result = proxyService.getOrderPage(page, size, status, staffId, startDate, endDate);
            return Result.success(result);
        } catch (Exception e) {
            return Result.failed("获取订单列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单详情
     */
    @GetMapping("/orders/{id}")
    public Result<ProxyOrder> getOrderDetail(@PathVariable Integer id) {
        try {
            ProxyOrder order = proxyService.getOrderById(id);
            if (order == null) {
                return Result.failed("订单不存在");
            }
            return Result.success(order);
        } catch (Exception e) {
            return Result.failed("获取订单详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新订单状态
     */
    @PutMapping("/orders/{id}/status")
    public Result<String> updateOrderStatus(@PathVariable Integer id, 
                                           @RequestParam String status) {
        try {
            boolean success = proxyService.updateOrderStatus(id, status);
            if (success) {
                return Result.success("订单状态更新成功");
            } else {
                return Result.failed("订单状态更新失败");
            }
        } catch (Exception e) {
            return Result.failed("更新订单状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 分配服务人员
     */
    @PutMapping("/orders/{orderId}/assign/{staffId}")
    public Result<String> assignStaff(@PathVariable Integer orderId, 
                                     @PathVariable Integer staffId) {
        try {
            boolean success = proxyService.assignStaff(orderId, staffId);
            if (success) {
                return Result.success("服务人员分配成功");
            } else {
                return Result.failed("服务人员分配失败");
            }
        } catch (Exception e) {
            return Result.failed("分配服务人员失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单统计信息
     */
    @GetMapping("/orders/statistics")
    public Result<Map<String, Object>> getOrderStatistics(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        try {
            if (startDate == null) {
                startDate = LocalDate.now().minusDays(30);
            }
            if (endDate == null) {
                endDate = LocalDate.now();
            }
            
            Map<String, Object> statistics = proxyService.getOrderStatistics(startDate, endDate);
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.failed("获取订单统计失败: " + e.getMessage());
        }
    }
    
    // ==================== 服务报告管理 ====================
    
    /**
     * 创建服务报告
     */
    @PostMapping("/reports")
    public Result<String> createServiceReport(@Valid @RequestBody ProxyServiceReport report) {
        try {
            boolean success = proxyService.createServiceReport(report);
            if (success) {
                return Result.success("服务报告创建成功");
            } else {
                return Result.failed("服务报告创建失败");
            }
        } catch (Exception e) {
            return Result.failed("创建服务报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务报告
     */
    @GetMapping("/reports/order/{orderId}")
    public Result<ProxyServiceReport> getServiceReport(@PathVariable Integer orderId) {
        try {
            ProxyServiceReport report = proxyService.getServiceReportByOrderId(orderId);
            return Result.success(report);
        } catch (Exception e) {
            return Result.failed("获取服务报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新服务报告
     */
    @PutMapping("/reports/{id}")
    public Result<String> updateServiceReport(@PathVariable Integer id, 
                                             @Valid @RequestBody ProxyServiceReport report) {
        try {
            report.setId(id);
            boolean success = proxyService.updateServiceReport(report);
            if (success) {
                return Result.success("服务报告更新成功");
            } else {
                return Result.failed("服务报告更新失败");
            }
        } catch (Exception e) {
            return Result.failed("更新服务报告失败: " + e.getMessage());
        }
    }
}
