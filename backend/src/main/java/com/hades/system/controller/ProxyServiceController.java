package com.hades.system.controller;

import com.hades.system.common.Result;
import com.hades.system.dto.ProxyOrderRequest;
import com.hades.system.dto.ProxyRatingRequest;
import com.hades.system.entity.*;
import com.hades.system.service.ProxyService;
import com.hades.system.utils.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 代客服务控制器
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@RestController
@RequestMapping("/proxy")
public class ProxyServiceController {
    
    @Autowired
    private ProxyService proxyService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 获取服务套餐列表
     */
    @GetMapping("/packages")
    public Result<List<ProxyPackage>> getPackages() {
        try {
            List<ProxyPackage> packages = proxyService.getActivePackages();
            return Result.success(packages);
        } catch (Exception e) {
            return Result.failed("获取套餐列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取套餐详情
     */
    @GetMapping("/package/{id}")
    public Result<ProxyPackage> getPackageDetail(@PathVariable Integer id) {
        try {
            ProxyPackage proxyPackage = proxyService.getPackageById(id);
            if (proxyPackage == null) {
                return Result.failed("套餐不存在");
            }
            return Result.success(proxyPackage);
        } catch (Exception e) {
            return Result.failed("获取套餐详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建代客订单
     */
    @PostMapping("/order")
    public Result<String> createOrder(@Valid @RequestBody ProxyOrderRequest request, 
                                     HttpServletRequest httpRequest) {
        try {
            Integer userId = jwtUtil.getUserIdFromRequest(httpRequest);
            String orderNo = proxyService.createOrder(userId, request);
            return Result.success(orderNo, "订单创建成功");
        } catch (Exception e) {
            return Result.failed("创建订单失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取用户订单列表
     */
    @GetMapping("/orders")
    public Result<List<ProxyOrder>> getOrders(HttpServletRequest httpRequest) {
        try {
            Integer userId = jwtUtil.getUserIdFromRequest(httpRequest);
            List<ProxyOrder> orders = proxyService.getOrdersByUserId(userId);
            return Result.success(orders);
        } catch (Exception e) {
            return Result.failed("获取订单列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单详情
     */
    @GetMapping("/order/{id}")
    public Result<ProxyOrder> getOrderDetail(@PathVariable Integer id, 
                                           HttpServletRequest httpRequest) {
        try {
            Integer userId = jwtUtil.getUserIdFromRequest(httpRequest);
            ProxyOrder order = proxyService.getOrderById(id);
            
            if (order == null) {
                return Result.failed("订单不存在");
            }
            
            // 验证订单所有权
            if (!order.getUserId().equals(userId)) {
                return Result.failed("无权限访问此订单");
            }
            
            return Result.success(order);
        } catch (Exception e) {
            return Result.failed("获取订单详情失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取服务报告
     */
    @GetMapping("/report/{orderId}")
    public Result<ProxyServiceReport> getServiceReport(@PathVariable Integer orderId, 
                                                      HttpServletRequest httpRequest) {
        try {
            Integer userId = jwtUtil.getUserIdFromRequest(httpRequest);
            
            // 验证订单所有权
            ProxyOrder order = proxyService.getOrderById(orderId);
            if (order == null || !order.getUserId().equals(userId)) {
                return Result.failed("订单不存在或无权限访问");
            }
            
            ProxyServiceReport report = proxyService.getServiceReportByOrderId(orderId);
            if (report == null) {
                return Result.failed("服务报告不存在");
            }
            
            return Result.success(report);
        } catch (Exception e) {
            return Result.failed("获取服务报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单照片
     */
    @GetMapping("/order/{orderId}/photos")
    public Result<List<ProxyOrderPhoto>> getOrderPhotos(@PathVariable Integer orderId, 
                                                       HttpServletRequest httpRequest) {
        try {
            Integer userId = jwtUtil.getUserIdFromRequest(httpRequest);
            
            // 验证订单所有权
            ProxyOrder order = proxyService.getOrderById(orderId);
            if (order == null || !order.getUserId().equals(userId)) {
                return Result.failed("订单不存在或无权限访问");
            }
            
            List<ProxyOrderPhoto> photos = proxyService.getOrderPhotos(orderId);
            return Result.success(photos);
        } catch (Exception e) {
            return Result.failed("获取订单照片失败: " + e.getMessage());
        }
    }
    
    /**
     * 评价服务
     */
    @PostMapping("/order/{orderId}/rate")
    public Result<String> rateService(@PathVariable Integer orderId, 
                                     @Valid @RequestBody ProxyRatingRequest request,
                                     HttpServletRequest httpRequest) {
        try {
            Integer userId = jwtUtil.getUserIdFromRequest(httpRequest);
            
            // 检查是否已评价
            ProxyOrderRating existingRating = proxyService.getRatingByOrderId(orderId);
            if (existingRating != null) {
                return Result.failed("该订单已评价");
            }
            
            boolean success = proxyService.createRating(userId, orderId, request);
            if (success) {
                return Result.success("评价成功");
            } else {
                return Result.failed("评价失败");
            }
        } catch (Exception e) {
            return Result.failed("评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取订单评价
     */
    @GetMapping("/order/{orderId}/rating")
    public Result<ProxyOrderRating> getOrderRating(@PathVariable Integer orderId, 
                                                  HttpServletRequest httpRequest) {
        try {
            Integer userId = jwtUtil.getUserIdFromRequest(httpRequest);
            
            // 验证订单所有权
            ProxyOrder order = proxyService.getOrderById(orderId);
            if (order == null || !order.getUserId().equals(userId)) {
                return Result.failed("订单不存在或无权限访问");
            }
            
            ProxyOrderRating rating = proxyService.getRatingByOrderId(orderId);
            return Result.success(rating);
        } catch (Exception e) {
            return Result.failed("获取评价失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消订单
     */
    @DeleteMapping("/order/{id}")
    public Result<String> cancelOrder(@PathVariable Integer id, 
                                     HttpServletRequest httpRequest) {
        try {
            Integer userId = jwtUtil.getUserIdFromRequest(httpRequest);
            
            // 验证订单所有权
            ProxyOrder order = proxyService.getOrderById(id);
            if (order == null || !order.getUserId().equals(userId)) {
                return Result.failed("订单不存在或无权限操作");
            }
            
            // 只有待确认和已确认的订单可以取消
            if (!"pending".equals(order.getStatus()) && !"confirmed".equals(order.getStatus())) {
                return Result.failed("当前订单状态不允许取消");
            }
            
            boolean success = proxyService.cancelOrder(id);
            if (success) {
                return Result.success("订单取消成功");
            } else {
                return Result.failed("订单取消失败");
            }
        } catch (Exception e) {
            return Result.failed("取消订单失败: " + e.getMessage());
        }
    }
}
