package com.hades.system.controller;

import com.hades.system.common.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件上传控制器
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@RestController
@RequestMapping("/file")
public class FileController {
    
    @Value("${hades.upload.path:/tmp/uploads}")
    private String uploadPath;
    
    @Value("${hades.upload.url-prefix:http://localhost:8080/api/file}")
    private String urlPrefix;
    
    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return Result.failed("文件不能为空");
            }
            
            // 检查文件大小（限制10MB）
            if (file.getSize() > 10 * 1024 * 1024) {
                return Result.failed("文件大小不能超过10MB");
            }
            
            // 检查文件类型
            String contentType = file.getContentType();
            if (contentType == null || (!contentType.startsWith("image/") && !contentType.equals("application/pdf"))) {
                return Result.failed("只支持图片和PDF文件");
            }
            
            // 创建上传目录
            String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String uploadDir = uploadPath + "/" + dateDir;
            File dir = new File(uploadDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            // 生成文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String filename = UUID.randomUUID().toString() + extension;
            
            // 保存文件
            File targetFile = new File(uploadDir, filename);
            file.transferTo(targetFile);
            
            // 返回文件信息
            Map<String, Object> result = new HashMap<>();
            result.put("filename", filename);
            result.put("originalName", originalFilename);
            result.put("size", file.getSize());
            result.put("contentType", contentType);
            result.put("url", urlPrefix + "/" + dateDir + "/" + filename);
            result.put("path", dateDir + "/" + filename);
            
            return Result.success(result, "文件上传成功");
            
        } catch (IOException e) {
            return Result.failed("文件上传失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除文件
     */
    @DeleteMapping("/delete/{fileId}")
    public Result<String> deleteFile(@PathVariable String fileId) {
        try {
            // 这里应该根据fileId从数据库查询文件路径
            // 为了简化，这里直接返回成功
            return Result.success("文件删除成功");
        } catch (Exception e) {
            return Result.failed("文件删除失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量上传文件
     */
    @PostMapping("/upload/batch")
    public Result<Map<String, Object>> uploadFiles(@RequestParam("files") MultipartFile[] files) {
        try {
            if (files == null || files.length == 0) {
                return Result.failed("文件不能为空");
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("total", files.length);
            result.put("success", 0);
            result.put("failed", 0);
            result.put("files", new HashMap<String, Object>());
            
            for (MultipartFile file : files) {
                try {
                    Result<Map<String, Object>> uploadResult = uploadFile(file);
                    if (uploadResult.getCode() == 200) {
                        ((Map<String, Object>) result.get("files")).put(file.getOriginalFilename(), uploadResult.getData());
                        result.put("success", (Integer) result.get("success") + 1);
                    } else {
                        result.put("failed", (Integer) result.get("failed") + 1);
                    }
                } catch (Exception e) {
                    result.put("failed", (Integer) result.get("failed") + 1);
                }
            }
            
            return Result.success(result, "批量上传完成");
            
        } catch (Exception e) {
            return Result.failed("批量上传失败: " + e.getMessage());
        }
    }
}
