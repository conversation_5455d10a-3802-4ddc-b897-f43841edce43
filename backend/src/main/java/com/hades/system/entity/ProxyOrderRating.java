package com.hades.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 代客服务评价实体类
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@TableName("proxy_order_ratings")
public class ProxyOrderRating {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    private Integer orderId;
    private Integer userId;
    private Integer staffId;
    private Integer serviceRating;
    private Integer attitudeRating;
    private Integer timelinessRating;
    private BigDecimal overallRating;
    private String comment;
    private String photos;
    private Boolean isAnonymous;
    
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    // 关联信息（非数据库字段）
    @TableField(exist = false)
    private String orderNo;
    
    @TableField(exist = false)
    private String userName;
    
    @TableField(exist = false)
    private String staffName;
    
    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    
    public Integer getOrderId() { return orderId; }
    public void setOrderId(Integer orderId) { this.orderId = orderId; }
    
    public Integer getUserId() { return userId; }
    public void setUserId(Integer userId) { this.userId = userId; }
    
    public Integer getStaffId() { return staffId; }
    public void setStaffId(Integer staffId) { this.staffId = staffId; }
    
    public Integer getServiceRating() { return serviceRating; }
    public void setServiceRating(Integer serviceRating) { this.serviceRating = serviceRating; }
    
    public Integer getAttitudeRating() { return attitudeRating; }
    public void setAttitudeRating(Integer attitudeRating) { this.attitudeRating = attitudeRating; }
    
    public Integer getTimelinessRating() { return timelinessRating; }
    public void setTimelinessRating(Integer timelinessRating) { this.timelinessRating = timelinessRating; }
    
    public BigDecimal getOverallRating() { return overallRating; }
    public void setOverallRating(BigDecimal overallRating) { this.overallRating = overallRating; }
    
    public String getComment() { return comment; }
    public void setComment(String comment) { this.comment = comment; }
    
    public String getPhotos() { return photos; }
    public void setPhotos(String photos) { this.photos = photos; }
    
    public Boolean getIsAnonymous() { return isAnonymous; }
    public void setIsAnonymous(Boolean isAnonymous) { this.isAnonymous = isAnonymous; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public String getOrderNo() { return orderNo; }
    public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
    
    public String getUserName() { return userName; }
    public void setUserName(String userName) { this.userName = userName; }
    
    public String getStaffName() { return staffName; }
    public void setStaffName(String staffName) { this.staffName = staffName; }
}
