package com.hades.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 代客订单实体类
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@TableName("proxy_orders")
public class ProxyOrder {
    
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    private String orderNo;
    private Integer userId;
    private Integer packageId;
    private Integer staffId;
    private Integer cemeteryId;
    private Integer graveSiteId;
    private String contactName;
    private String contactPhone;
    private String deceasedName;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate appointmentDate;
    
    private String appointmentTime;
    private String specialRequirements;
    private BigDecimal totalAmount;
    private BigDecimal paidAmount;
    private String status;
    private String paymentStatus;
    
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // 关联信息（非数据库字段）
    @TableField(exist = false)
    private String userName;
    
    @TableField(exist = false)
    private String packageName;
    
    @TableField(exist = false)
    private String staffName;
    
    @TableField(exist = false)
    private String cemeteryName;
    
    @TableField(exist = false)
    private String graveSiteNumber;
    
    // Getters and Setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }
    
    public String getOrderNo() { return orderNo; }
    public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
    
    public Integer getUserId() { return userId; }
    public void setUserId(Integer userId) { this.userId = userId; }
    
    public Integer getPackageId() { return packageId; }
    public void setPackageId(Integer packageId) { this.packageId = packageId; }
    
    public Integer getStaffId() { return staffId; }
    public void setStaffId(Integer staffId) { this.staffId = staffId; }
    
    public Integer getCemeteryId() { return cemeteryId; }
    public void setCemeteryId(Integer cemeteryId) { this.cemeteryId = cemeteryId; }
    
    public Integer getGraveSiteId() { return graveSiteId; }
    public void setGraveSiteId(Integer graveSiteId) { this.graveSiteId = graveSiteId; }
    
    public String getContactName() { return contactName; }
    public void setContactName(String contactName) { this.contactName = contactName; }
    
    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }
    
    public String getDeceasedName() { return deceasedName; }
    public void setDeceasedName(String deceasedName) { this.deceasedName = deceasedName; }
    
    public LocalDate getAppointmentDate() { return appointmentDate; }
    public void setAppointmentDate(LocalDate appointmentDate) { this.appointmentDate = appointmentDate; }
    
    public String getAppointmentTime() { return appointmentTime; }
    public void setAppointmentTime(String appointmentTime) { this.appointmentTime = appointmentTime; }
    
    public String getSpecialRequirements() { return specialRequirements; }
    public void setSpecialRequirements(String specialRequirements) { this.specialRequirements = specialRequirements; }
    
    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
    
    public BigDecimal getPaidAmount() { return paidAmount; }
    public void setPaidAmount(BigDecimal paidAmount) { this.paidAmount = paidAmount; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
    
    public String getPaymentStatus() { return paymentStatus; }
    public void setPaymentStatus(String paymentStatus) { this.paymentStatus = paymentStatus; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public String getUserName() { return userName; }
    public void setUserName(String userName) { this.userName = userName; }
    
    public String getPackageName() { return packageName; }
    public void setPackageName(String packageName) { this.packageName = packageName; }
    
    public String getStaffName() { return staffName; }
    public void setStaffName(String staffName) { this.staffName = staffName; }
    
    public String getCemeteryName() { return cemeteryName; }
    public void setCemeteryName(String cemeteryName) { this.cemeteryName = cemeteryName; }
    
    public String getGraveSiteNumber() { return graveSiteNumber; }
    public void setGraveSiteNumber(String graveSiteNumber) { this.graveSiteNumber = graveSiteNumber; }
}
