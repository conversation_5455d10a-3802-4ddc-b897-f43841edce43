package com.hades.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hades.system.dto.ProxyOrderRequest;
import com.hades.system.dto.ProxyRatingRequest;
import com.hades.system.entity.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 代客服务接口
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
public interface ProxyService {
    
    // ==================== 套餐管理 ====================
    
    /**
     * 获取所有活跃套餐
     */
    List<ProxyPackage> getActivePackages();
    
    /**
     * 获取套餐分页列表
     */
    Page<ProxyPackage> getPackagePage(int page, int size, String name, String status);
    
    /**
     * 根据ID获取套餐详情
     */
    ProxyPackage getPackageById(Integer id);
    
    /**
     * 创建套餐
     */
    boolean createPackage(ProxyPackage proxyPackage);
    
    /**
     * 更新套餐
     */
    boolean updatePackage(ProxyPackage proxyPackage);
    
    /**
     * 删除套餐
     */
    boolean deletePackage(Integer id);
    
    // ==================== 服务人员管理 ====================
    
    /**
     * 获取所有活跃服务人员
     */
    List<ProxyStaff> getActiveStaff();
    
    /**
     * 获取服务人员分页列表
     */
    Page<ProxyStaff> getStaffPage(int page, int size, String name, String status);
    
    /**
     * 根据ID获取服务人员详情
     */
    ProxyStaff getStaffById(Integer id);
    
    /**
     * 创建服务人员
     */
    boolean createStaff(ProxyStaff proxyStaff);
    
    /**
     * 更新服务人员
     */
    boolean updateStaff(ProxyStaff proxyStaff);
    
    /**
     * 删除服务人员
     */
    boolean deleteStaff(Integer id);
    
    // ==================== 订单管理 ====================
    
    /**
     * 创建代客订单
     */
    String createOrder(Integer userId, ProxyOrderRequest request);
    
    /**
     * 获取订单分页列表
     */
    Page<ProxyOrder> getOrderPage(int page, int size, String status, Integer staffId, 
                                 LocalDate startDate, LocalDate endDate);
    
    /**
     * 根据用户ID获取订单列表
     */
    List<ProxyOrder> getOrdersByUserId(Integer userId);
    
    /**
     * 根据ID获取订单详情
     */
    ProxyOrder getOrderById(Integer id);
    
    /**
     * 更新订单状态
     */
    boolean updateOrderStatus(Integer id, String status);
    
    /**
     * 分配服务人员
     */
    boolean assignStaff(Integer orderId, Integer staffId);
    
    /**
     * 取消订单
     */
    boolean cancelOrder(Integer id);
    
    /**
     * 获取订单统计信息
     */
    Map<String, Object> getOrderStatistics(LocalDate startDate, LocalDate endDate);
    
    // ==================== 服务报告管理 ====================
    
    /**
     * 创建服务报告
     */
    boolean createServiceReport(ProxyServiceReport report);
    
    /**
     * 根据订单ID获取服务报告
     */
    ProxyServiceReport getServiceReportByOrderId(Integer orderId);
    
    /**
     * 更新服务报告
     */
    boolean updateServiceReport(ProxyServiceReport report);
    
    // ==================== 照片管理 ====================
    
    /**
     * 添加服务照片
     */
    boolean addOrderPhoto(ProxyOrderPhoto photo);
    
    /**
     * 根据订单ID获取照片列表
     */
    List<ProxyOrderPhoto> getOrderPhotos(Integer orderId);
    
    /**
     * 删除照片
     */
    boolean deletePhoto(Integer id);
    
    // ==================== 评价管理 ====================
    
    /**
     * 创建服务评价
     */
    boolean createRating(Integer userId, Integer orderId, ProxyRatingRequest request);
    
    /**
     * 根据订单ID获取评价
     */
    ProxyOrderRating getRatingByOrderId(Integer orderId);
    
    /**
     * 根据服务人员ID获取评价列表
     */
    List<ProxyOrderRating> getRatingsByStaffId(Integer staffId);
}
