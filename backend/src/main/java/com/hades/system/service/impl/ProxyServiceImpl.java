package com.hades.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hades.system.dto.ProxyOrderRequest;
import com.hades.system.dto.ProxyRatingRequest;
import com.hades.system.entity.*;
import com.hades.system.mapper.*;
import com.hades.system.service.ProxyService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 代客服务实现类
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
@Service
public class ProxyServiceImpl implements ProxyService {
    
    @Autowired
    private ProxyPackageMapper proxyPackageMapper;
    
    @Autowired
    private ProxyStaffMapper proxyStaffMapper;
    
    @Autowired
    private ProxyOrderMapper proxyOrderMapper;
    
    @Autowired
    private ProxyServiceReportMapper proxyServiceReportMapper;
    
    @Autowired
    private ProxyOrderPhotoMapper proxyOrderPhotoMapper;
    
    @Autowired
    private ProxyOrderRatingMapper proxyOrderRatingMapper;
    
    // ==================== 套餐管理 ====================
    
    @Override
    public List<ProxyPackage> getActivePackages() {
        List<ProxyPackage> packages = proxyPackageMapper.selectActivePackages();
        // 解析features JSON字符串
        packages.forEach(pkg -> {
            if (StringUtils.hasText(pkg.getFeatures())) {
                try {
                    List<String> featureList = JSON.parseArray(pkg.getFeatures(), String.class);
                    pkg.setFeatureList(featureList);
                } catch (Exception e) {
                    // 忽略JSON解析错误
                }
            }
        });
        return packages;
    }
    
    @Override
    public Page<ProxyPackage> getPackagePage(int page, int size, String name, String status) {
        Page<ProxyPackage> pageParam = new Page<>(page, size);
        QueryWrapper<ProxyPackage> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByAsc("sort_order").orderByDesc("created_at");
        return proxyPackageMapper.selectPage(pageParam, queryWrapper);
    }
    
    @Override
    public ProxyPackage getPackageById(Integer id) {
        ProxyPackage pkg = proxyPackageMapper.selectById(id);
        if (pkg != null && StringUtils.hasText(pkg.getFeatures())) {
            try {
                List<String> featureList = JSON.parseArray(pkg.getFeatures(), String.class);
                pkg.setFeatureList(featureList);
            } catch (Exception e) {
                // 忽略JSON解析错误
            }
        }
        return pkg;
    }
    
    @Override
    public boolean createPackage(ProxyPackage proxyPackage) {
        // 处理features列表转JSON
        if (proxyPackage.getFeatureList() != null && !proxyPackage.getFeatureList().isEmpty()) {
            proxyPackage.setFeatures(JSON.toJSONString(proxyPackage.getFeatureList()));
        }
        return proxyPackageMapper.insert(proxyPackage) > 0;
    }
    
    @Override
    public boolean updatePackage(ProxyPackage proxyPackage) {
        // 处理features列表转JSON
        if (proxyPackage.getFeatureList() != null && !proxyPackage.getFeatureList().isEmpty()) {
            proxyPackage.setFeatures(JSON.toJSONString(proxyPackage.getFeatureList()));
        }
        return proxyPackageMapper.updateById(proxyPackage) > 0;
    }
    
    @Override
    public boolean deletePackage(Integer id) {
        return proxyPackageMapper.deleteById(id) > 0;
    }
    
    // ==================== 服务人员管理 ====================
    
    @Override
    public List<ProxyStaff> getActiveStaff() {
        return proxyStaffMapper.selectActiveStaff();
    }
    
    @Override
    public Page<ProxyStaff> getStaffPage(int page, int size, String name, String status) {
        Page<ProxyStaff> pageParam = new Page<>(page, size);
        QueryWrapper<ProxyStaff> queryWrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(name)) {
            queryWrapper.like("name", name);
        }
        if (StringUtils.hasText(status)) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByDesc("rating").orderByDesc("total_orders");
        return proxyStaffMapper.selectPage(pageParam, queryWrapper);
    }
    
    @Override
    public ProxyStaff getStaffById(Integer id) {
        return proxyStaffMapper.selectById(id);
    }
    
    @Override
    public boolean createStaff(ProxyStaff proxyStaff) {
        return proxyStaffMapper.insert(proxyStaff) > 0;
    }
    
    @Override
    public boolean updateStaff(ProxyStaff proxyStaff) {
        return proxyStaffMapper.updateById(proxyStaff) > 0;
    }
    
    @Override
    public boolean deleteStaff(Integer id) {
        return proxyStaffMapper.deleteById(id) > 0;
    }
    
    // ==================== 订单管理 ====================
    
    @Override
    @Transactional
    public String createOrder(Integer userId, ProxyOrderRequest request) {
        // 获取套餐信息
        ProxyPackage proxyPackage = proxyPackageMapper.selectById(request.getPackageId());
        if (proxyPackage == null) {
            throw new RuntimeException("套餐不存在");
        }
        
        // 生成订单号
        String orderNo = generateOrderNo();
        
        // 创建订单
        ProxyOrder order = new ProxyOrder();
        BeanUtils.copyProperties(request, order);
        order.setOrderNo(orderNo);
        order.setUserId(userId);
        order.setTotalAmount(proxyPackage.getPrice());
        order.setPaidAmount(BigDecimal.ZERO);
        order.setStatus("pending");
        order.setPaymentStatus("unpaid");
        
        proxyOrderMapper.insert(order);
        return orderNo;
    }
    
    @Override
    public Page<ProxyOrder> getOrderPage(int page, int size, String status, Integer staffId, 
                                        LocalDate startDate, LocalDate endDate) {
        Page<ProxyOrder> pageParam = new Page<>(page, size);
        return proxyOrderMapper.selectOrderPageWithDetails(pageParam, status, staffId, startDate, endDate);
    }
    
    @Override
    public List<ProxyOrder> getOrdersByUserId(Integer userId) {
        return proxyOrderMapper.selectOrdersByUserId(userId);
    }
    
    @Override
    public ProxyOrder getOrderById(Integer id) {
        return proxyOrderMapper.selectById(id);
    }
    
    @Override
    public boolean updateOrderStatus(Integer id, String status) {
        ProxyOrder order = new ProxyOrder();
        order.setId(id);
        order.setStatus(status);
        return proxyOrderMapper.updateById(order) > 0;
    }
    
    @Override
    public boolean assignStaff(Integer orderId, Integer staffId) {
        ProxyOrder order = new ProxyOrder();
        order.setId(orderId);
        order.setStaffId(staffId);
        order.setStatus("assigned");
        return proxyOrderMapper.updateById(order) > 0;
    }
    
    @Override
    public boolean cancelOrder(Integer id) {
        ProxyOrder order = new ProxyOrder();
        order.setId(id);
        order.setStatus("cancelled");
        return proxyOrderMapper.updateById(order) > 0;
    }
    
    @Override
    public Map<String, Object> getOrderStatistics(LocalDate startDate, LocalDate endDate) {
        return proxyOrderMapper.getOrderStatistics(startDate, endDate);
    }
    
    // ==================== 服务报告管理 ====================
    
    @Override
    public boolean createServiceReport(ProxyServiceReport report) {
        return proxyServiceReportMapper.insert(report) > 0;
    }
    
    @Override
    public ProxyServiceReport getServiceReportByOrderId(Integer orderId) {
        return proxyServiceReportMapper.selectByOrderId(orderId);
    }
    
    @Override
    public boolean updateServiceReport(ProxyServiceReport report) {
        return proxyServiceReportMapper.updateById(report) > 0;
    }
    
    // ==================== 照片管理 ====================
    
    @Override
    public boolean addOrderPhoto(ProxyOrderPhoto photo) {
        return proxyOrderPhotoMapper.insert(photo) > 0;
    }
    
    @Override
    public List<ProxyOrderPhoto> getOrderPhotos(Integer orderId) {
        return proxyOrderPhotoMapper.selectByOrderId(orderId);
    }
    
    @Override
    public boolean deletePhoto(Integer id) {
        return proxyOrderPhotoMapper.deleteById(id) > 0;
    }
    
    // ==================== 评价管理 ====================
    
    @Override
    @Transactional
    public boolean createRating(Integer userId, Integer orderId, ProxyRatingRequest request) {
        // 获取订单信息
        ProxyOrder order = proxyOrderMapper.selectById(orderId);
        if (order == null || !order.getUserId().equals(userId)) {
            throw new RuntimeException("订单不存在或无权限");
        }
        
        // 计算综合评分
        BigDecimal overallRating = BigDecimal.valueOf(
            (request.getServiceRating() + request.getAttitudeRating() + request.getTimelinessRating()) / 3.0
        ).setScale(2, RoundingMode.HALF_UP);
        
        // 创建评价
        ProxyOrderRating rating = new ProxyOrderRating();
        BeanUtils.copyProperties(request, rating);
        rating.setOrderId(orderId);
        rating.setUserId(userId);
        rating.setStaffId(order.getStaffId());
        rating.setOverallRating(overallRating);
        
        boolean result = proxyOrderRatingMapper.insert(rating) > 0;
        
        // 更新服务人员评分
        if (result && order.getStaffId() != null) {
            BigDecimal avgRating = proxyOrderRatingMapper.calculateStaffAverageRating(order.getStaffId());
            if (avgRating != null) {
                proxyStaffMapper.updateStaffRatingAndOrders(order.getStaffId(), avgRating);
            }
        }
        
        return result;
    }
    
    @Override
    public ProxyOrderRating getRatingByOrderId(Integer orderId) {
        return proxyOrderRatingMapper.selectByOrderId(orderId);
    }
    
    @Override
    public List<ProxyOrderRating> getRatingsByStaffId(Integer staffId) {
        return proxyOrderRatingMapper.selectByStaffId(staffId);
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        return "PX" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + 
               String.format("%04d", (int)(Math.random() * 10000));
    }
}
