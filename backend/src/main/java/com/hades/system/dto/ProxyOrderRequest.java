package com.hades.system.dto;

import java.time.LocalDate;

/**
 * 代客订单请求DTO
 *
 * <AUTHOR> System
 * @version 1.0.0
 */
public class ProxyOrderRequest {
    
    private Integer packageId;
    private Integer cemeteryId;
    private Integer graveSiteId;
    private String contactName;
    private String contactPhone;
    private String deceasedName;
    private LocalDate appointmentDate;
    private String appointmentTime;
    private String specialRequirements;
    
    // Getters and Setters
    public Integer getPackageId() { return packageId; }
    public void setPackageId(Integer packageId) { this.packageId = packageId; }
    
    public Integer getCemeteryId() { return cemeteryId; }
    public void setCemeteryId(Integer cemeteryId) { this.cemeteryId = cemeteryId; }
    
    public Integer getGraveSiteId() { return graveSiteId; }
    public void setGraveSiteId(Integer graveSiteId) { this.graveSiteId = graveSiteId; }
    
    public String getContactName() { return contactName; }
    public void setContactName(String contactName) { this.contactName = contactName; }
    
    public String getContactPhone() { return contactPhone; }
    public void setContactPhone(String contactPhone) { this.contactPhone = contactPhone; }
    
    public String getDeceasedName() { return deceasedName; }
    public void setDeceasedName(String deceasedName) { this.deceasedName = deceasedName; }
    
    public LocalDate getAppointmentDate() { return appointmentDate; }
    public void setAppointmentDate(LocalDate appointmentDate) { this.appointmentDate = appointmentDate; }
    
    public String getAppointmentTime() { return appointmentTime; }
    public void setAppointmentTime(String appointmentTime) { this.appointmentTime = appointmentTime; }
    
    public String getSpecialRequirements() { return specialRequirements; }
    public void setSpecialRequirements(String specialRequirements) { this.specialRequirements = specialRequirements; }
}
