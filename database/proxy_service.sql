-- 代客扫墓服务相关数据库表设计
-- 创建时间: 2025-07-28

-- 1. 服务套餐表
CREATE TABLE proxy_packages (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '套餐ID',
    name VARCHAR(100) NOT NULL COMMENT '套餐名称',
    description TEXT COMMENT '套餐描述',
    price DECIMAL(10,2) NOT NULL COMMENT '套餐价格',
    duration INT NOT NULL DEFAULT 60 COMMENT '服务时长(分钟)',
    features JSON COMMENT '套餐特色功能',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代客服务套餐表';

-- 2. 服务人员表
CREATE TABLE proxy_staff (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '人员ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    id_card VARCHAR(18) COMMENT '身份证号',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    status ENUM('active', 'inactive', 'busy') DEFAULT 'active' COMMENT '状态',
    rating DECIMAL(3,2) DEFAULT 5.00 COMMENT '评分',
    total_orders INT DEFAULT 0 COMMENT '总订单数',
    experience_years INT DEFAULT 0 COMMENT '工作经验(年)',
    specialties VARCHAR(200) COMMENT '专长描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_phone (phone),
    INDEX idx_status (status),
    INDEX idx_rating (rating)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代客服务人员表';

-- 3. 代客订单表
CREATE TABLE proxy_orders (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_no VARCHAR(32) NOT NULL COMMENT '订单号',
    user_id INT NOT NULL COMMENT '用户ID',
    package_id INT NOT NULL COMMENT '套餐ID',
    staff_id INT COMMENT '服务人员ID',
    cemetery_id INT COMMENT '墓园ID',
    grave_site_id INT COMMENT '墓位ID',
    contact_name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系人电话',
    deceased_name VARCHAR(50) COMMENT '逝者姓名',
    appointment_date DATE NOT NULL COMMENT '预约日期',
    appointment_time VARCHAR(20) NOT NULL COMMENT '预约时间',
    special_requirements TEXT COMMENT '特殊要求',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    paid_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '已支付金额',
    status ENUM('pending', 'confirmed', 'assigned', 'in_progress', 'completed', 'cancelled', 'refunded') 
           DEFAULT 'pending' COMMENT '订单状态',
    payment_status ENUM('unpaid', 'paid', 'refunded') DEFAULT 'unpaid' COMMENT '支付状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_staff_id (staff_id),
    INDEX idx_status (status),
    INDEX idx_appointment_date (appointment_date),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (package_id) REFERENCES proxy_packages(id),
    FOREIGN KEY (staff_id) REFERENCES proxy_staff(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代客订单表';

-- 4. 服务报告表
CREATE TABLE proxy_service_reports (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '报告ID',
    order_id INT NOT NULL COMMENT '订单ID',
    staff_id INT NOT NULL COMMENT '服务人员ID',
    start_time TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP COMMENT '结束时间',
    service_content TEXT COMMENT '服务内容',
    weather VARCHAR(50) COMMENT '天气情况',
    grave_condition VARCHAR(200) COMMENT '墓位状况',
    service_items JSON COMMENT '服务项目清单',
    notes TEXT COMMENT '备注说明',
    status ENUM('draft', 'submitted', 'approved') DEFAULT 'draft' COMMENT '报告状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_order_id (order_id),
    INDEX idx_staff_id (staff_id),
    INDEX idx_status (status),
    FOREIGN KEY (order_id) REFERENCES proxy_orders(id),
    FOREIGN KEY (staff_id) REFERENCES proxy_staff(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代客服务报告表';

-- 5. 服务照片表
CREATE TABLE proxy_order_photos (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '照片ID',
    order_id INT NOT NULL COMMENT '订单ID',
    report_id INT COMMENT '报告ID',
    photo_url VARCHAR(500) NOT NULL COMMENT '照片URL',
    photo_type ENUM('before', 'during', 'after', 'grave', 'flowers', 'other') 
               DEFAULT 'other' COMMENT '照片类型',
    description VARCHAR(200) COMMENT '照片描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_order_id (order_id),
    INDEX idx_report_id (report_id),
    INDEX idx_photo_type (photo_type),
    FOREIGN KEY (order_id) REFERENCES proxy_orders(id),
    FOREIGN KEY (report_id) REFERENCES proxy_service_reports(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代客服务照片表';

-- 6. 服务评价表
CREATE TABLE proxy_order_ratings (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '评价ID',
    order_id INT NOT NULL COMMENT '订单ID',
    user_id INT NOT NULL COMMENT '用户ID',
    staff_id INT NOT NULL COMMENT '服务人员ID',
    service_rating INT NOT NULL COMMENT '服务评分(1-5)',
    attitude_rating INT NOT NULL COMMENT '态度评分(1-5)',
    timeliness_rating INT NOT NULL COMMENT '及时性评分(1-5)',
    overall_rating DECIMAL(3,2) NOT NULL COMMENT '综合评分',
    comment TEXT COMMENT '评价内容',
    photos JSON COMMENT '评价照片',
    is_anonymous BOOLEAN DEFAULT FALSE COMMENT '是否匿名',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_order_user (order_id, user_id),
    INDEX idx_staff_id (staff_id),
    INDEX idx_overall_rating (overall_rating),
    FOREIGN KEY (order_id) REFERENCES proxy_orders(id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (staff_id) REFERENCES proxy_staff(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代客服务评价表';

-- 插入默认套餐数据
INSERT INTO proxy_packages (name, description, price, duration, features, sort_order) VALUES
('基础套餐', '基础清洁服务，包含墓碑清洁、献花等基本服务', 88.00, 30, 
 '["墓碑清洁", "献花", "基础整理", "服务照片"]', 1),
('标准套餐', '标准服务，包含基础服务加上祭品摆放、简单祭拜仪式', 168.00, 45, 
 '["墓碑清洁", "献花", "祭品摆放", "祭拜仪式", "详细报告", "服务视频"]', 2),
('尊崇套餐', '尊崇服务，包含全套服务加上专业祭拜、详细报告等', 288.00, 60, 
 '["墓碑清洁", "献花", "祭品摆放", "专业祭拜", "详细报告", "服务视频", "后续跟踪"]', 3);

-- 插入示例服务人员数据
INSERT INTO proxy_staff (name, phone, status, experience_years, specialties) VALUES
('张师傅', '13800138001', 'active', 5, '专业祭拜仪式，经验丰富'),
('李师傅', '13800138002', 'active', 3, '细致服务，态度亲和'),
('王师傅', '13800138003', 'active', 8, '资深服务，专业可靠');
