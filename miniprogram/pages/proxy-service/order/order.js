// pages/proxy-service/order/order.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    packageId: null,
    packageInfo: null,
    form: {
      contactName: '',
      contactPhone: '',
      deceasedName: '',
      appointmentDate: '',
      appointmentTime: '',
      specialRequirements: ''
    },
    timeSlots: [
      '09:00-10:00', '10:00-11:00', '11:00-12:00',
      '14:00-15:00', '15:00-16:00', '16:00-17:00'
    ],
    selectedTimeIndex: -1,
    loading: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.packageId) {
      this.setData({ packageId: options.packageId })
      this.loadPackageInfo()
    }

    // 设置默认日期为明天
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    const dateStr = tomorrow.toISOString().split('T')[0]
    this.setData({
      'form.appointmentDate': dateStr
    })
  },

  /**
   * 加载套餐信息
   */
  loadPackageInfo() {
    wx.request({
      url: `${app.globalData.apiUrl}/proxy/package/${this.data.packageId}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({ packageInfo: res.data.data })
        } else {
          wx.showToast({
            title: res.data.message || '加载套餐信息失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('加载套餐信息失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 输入框变化处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`form.${field}`]: value
    })
  },

  /**
   * 日期选择
   */
  onDateChange(e) {
    this.setData({
      'form.appointmentDate': e.detail.value
    })
  },

  /**
   * 时间段选择
   */
  onTimeSelect(e) {
    const index = e.currentTarget.dataset.index
    const timeSlot = this.data.timeSlots[index]
    this.setData({
      selectedTimeIndex: index,
      'form.appointmentTime': timeSlot
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { form } = this.data

    if (!form.contactName.trim()) {
      wx.showToast({ title: '请输入联系人姓名', icon: 'none' })
      return false
    }

    if (!form.contactPhone.trim()) {
      wx.showToast({ title: '请输入联系人电话', icon: 'none' })
      return false
    }

    if (!/^1[3-9]\d{9}$/.test(form.contactPhone)) {
      wx.showToast({ title: '请输入正确的手机号', icon: 'none' })
      return false
    }

    if (!form.appointmentDate) {
      wx.showToast({ title: '请选择预约日期', icon: 'none' })
      return false
    }

    if (!form.appointmentTime) {
      wx.showToast({ title: '请选择预约时间', icon: 'none' })
      return false
    }

    return true
  },

  /**
   * 提交订单
   */
  submitOrder() {
    if (!this.validateForm()) return

    this.setData({ loading: true })

    const orderData = {
      packageId: this.data.packageId,
      ...this.data.form
    }

    wx.request({
      url: `${app.globalData.apiUrl}/proxy/order`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      data: orderData,
      success: (res) => {
        this.setData({ loading: false })

        if (res.data.code === 200) {
          wx.showToast({
            title: '订单提交成功',
            icon: 'success'
          })

          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: res.data.message || '订单提交失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('提交订单失败:', err)
        this.setData({ loading: false })
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  }
})