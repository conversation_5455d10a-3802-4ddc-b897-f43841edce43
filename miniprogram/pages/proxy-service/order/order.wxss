/* pages/proxy-service/order/order.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* 套餐信息 */
.package-info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.package-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.package-price {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.package-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

.package-duration {
  font-size: 26rpx;
  color: #999;
}

/* 表单容器 */
.form-container {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 表单分组 */
.form-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}

.required {
  color: #ff6b6b;
  margin-left: 5rpx;
}

.form-input {
  width: 100%;
  padding: 25rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 25rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  box-sizing: border-box;
}

.form-textarea:focus {
  border-color: #667eea;
  background: white;
}

/* 日期选择器 */
.picker-input {
  width: 100%;
  padding: 25rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #fafafa;
  color: #333;
  box-sizing: border-box;
}

/* 时间段选择 */
.time-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.time-slot {
  flex: 0 0 calc(50% - 7.5rpx);
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #666;
  background: #fafafa;
  transition: all 0.3s ease;
}

.time-slot.selected {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.time-slot:active {
  transform: scale(0.95);
}

/* 提交容器 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx 20rpx;
  border-top: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.price-info {
  display: flex;
  align-items: center;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b6b;
  margin-left: 10rpx;
}

.submit-btn {
  flex: 1;
  margin-left: 30rpx;
  padding: 25rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.submit-btn.loading {
  opacity: 0.7;
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-btn[disabled] {
  opacity: 0.5;
}
