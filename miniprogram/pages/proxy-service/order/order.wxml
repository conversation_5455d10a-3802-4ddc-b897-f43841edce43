<!--pages/proxy-service/order/order.wxml-->
<view class="container">
  <!-- 套餐信息 -->
  <view wx:if="{{packageInfo}}" class="package-info">
    <view class="package-header">
      <view class="package-name">{{packageInfo.name}}</view>
      <view class="package-price">¥{{packageInfo.price}}</view>
    </view>
    <view class="package-description">{{packageInfo.description}}</view>
    <view class="package-duration">服务时长：{{packageInfo.duration}}分钟</view>
  </view>

  <!-- 订单表单 -->
  <view class="form-container">
    <!-- 联系人信息 -->
    <view class="form-section">
      <view class="section-title">联系人信息</view>

      <view class="form-item">
        <view class="form-label">联系人姓名 <text class="required">*</text></view>
        <input
          class="form-input"
          placeholder="请输入联系人姓名"
          value="{{form.contactName}}"
          data-field="contactName"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <view class="form-label">联系人电话 <text class="required">*</text></view>
        <input
          class="form-input"
          placeholder="请输入联系人电话"
          type="number"
          value="{{form.contactPhone}}"
          data-field="contactPhone"
          bindinput="onInputChange"
        />
      </view>

      <view class="form-item">
        <view class="form-label">逝者姓名</view>
        <input
          class="form-input"
          placeholder="请输入逝者姓名（可选）"
          value="{{form.deceasedName}}"
          data-field="deceasedName"
          bindinput="onInputChange"
        />
      </view>
    </view>

    <!-- 预约时间 -->
    <view class="form-section">
      <view class="section-title">预约时间</view>

      <view class="form-item">
        <view class="form-label">预约日期 <text class="required">*</text></view>
        <picker
          mode="date"
          value="{{form.appointmentDate}}"
          start="{{tomorrow}}"
          bindchange="onDateChange"
        >
          <view class="picker-input">
            {{form.appointmentDate || '请选择预约日期'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <view class="form-label">预约时间 <text class="required">*</text></view>
        <view class="time-slots">
          <view
            wx:for="{{timeSlots}}"
            wx:key="*this"
            class="time-slot {{selectedTimeIndex === index ? 'selected' : ''}}"
            data-index="{{index}}"
            bindtap="onTimeSelect"
          >
            {{item}}
          </view>
        </view>
      </view>
    </view>

    <!-- 特殊要求 -->
    <view class="form-section">
      <view class="section-title">特殊要求</view>

      <view class="form-item">
        <view class="form-label">备注说明</view>
        <textarea
          class="form-textarea"
          placeholder="请输入特殊要求或备注（可选）"
          value="{{form.specialRequirements}}"
          data-field="specialRequirements"
          bindinput="onInputChange"
          maxlength="200"
        />
      </view>
    </view>
  </view>

  <!-- 提交按钮 -->
  <view class="submit-container">
    <view class="price-info">
      <text class="price-label">总计：</text>
      <text class="price-value">¥{{packageInfo ? packageInfo.price : 0}}</text>
    </view>
    <button
      class="submit-btn {{loading ? 'loading' : ''}}"
      bindtap="submitOrder"
      disabled="{{loading}}"
    >
      {{loading ? '提交中...' : '确认预约'}}
    </button>
  </view>
</view>