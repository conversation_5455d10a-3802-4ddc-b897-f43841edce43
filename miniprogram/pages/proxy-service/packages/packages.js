// pages/proxy-service/packages/packages.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    packages: [],
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadPackages()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时刷新数据
    this.loadPackages()
  },

  /**
   * 加载套餐列表
   */
  loadPackages() {
    this.setData({ loading: true })

    wx.request({
      url: `${app.globalData.apiUrl}/proxy/packages`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      },
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            packages: res.data.data,
            loading: false
          })
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          })
          this.setData({ loading: false })
        }
      },
      fail: (err) => {
        console.error('加载套餐失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        this.setData({ loading: false })
      }
    })
  },

  /**
   * 选择套餐
   */
  selectPackage(e) {
    const packageId = e.currentTarget.dataset.id
    const packageInfo = this.data.packages.find(pkg => pkg.id === packageId)

    if (!packageInfo) {
      wx.showToast({
        title: '套餐信息错误',
        icon: 'none'
      })
      return
    }

    // 跳转到订单页面
    wx.navigateTo({
      url: `/pages/proxy-service/order/order?packageId=${packageId}`
    })
  },

  /**
   * 查看套餐详情
   */
  viewPackageDetail(e) {
    const packageId = e.currentTarget.dataset.id
    const packageInfo = this.data.packages.find(pkg => pkg.id === packageId)

    if (!packageInfo) return

    wx.showModal({
      title: packageInfo.name,
      content: `${packageInfo.description}\n\n价格：¥${packageInfo.price}\n时长：${packageInfo.duration}分钟\n\n特色服务：\n${packageInfo.featureList ? packageInfo.featureList.join('\n') : '暂无'}`,
      showCancel: true,
      cancelText: '返回',
      confirmText: '立即预约',
      success: (res) => {
        if (res.confirm) {
          this.selectPackage(e)
        }
      }
    })
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadPackages()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  }
})