/* pages/proxy-service/packages/packages.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  color: white;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

/* 套餐列表 */
.packages-list {
  margin-bottom: 30rpx;
}

.package-card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.package-card:active {
  transform: scale(0.98);
}

/* 套餐头部 */
.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.package-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.package-price {
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6b6b;
}

/* 套餐描述 */
.package-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

/* 服务时长 */
.package-duration {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.duration-label {
  font-size: 26rpx;
  color: #999;
}

.duration-value {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

/* 套餐特色 */
.package-features {
  margin-bottom: 30rpx;
}

.features-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.features-list {
  padding-left: 20rpx;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.feature-dot {
  color: #4CAF50;
  font-size: 24rpx;
  margin-right: 10rpx;
  margin-top: 4rpx;
}

.feature-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 操作按钮 */
.package-actions {
  display: flex;
  gap: 20rpx;
}

.btn-detail {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #666;
  background: white;
}

.btn-select {
  flex: 2;
  text-align: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50rpx;
  font-size: 28rpx;
  color: white;
  font-weight: bold;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
}

/* 底部说明 */
.footer-info {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-top: 30rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.info-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  white-space: pre-line;
}
