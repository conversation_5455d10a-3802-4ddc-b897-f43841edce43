// pages/profile/info/info.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    vehicles: [],
    favoriteGraveSites: [],
    loading: false,
    showEditModal: false,
    editForm: {
      nickname: '',
      phone: ''
    }
  },

  onLoad() {
    this.loadUserData()
  },

  onShow() {
    this.loadUserData()
  },

  // 加载用户数据
  async loadUserData() {
    this.setData({ loading: true })

    try {
      // 并行加载用户信息、车辆列表和收藏墓位
      const [userInfo, vehicles, favoriteGraveSites] = await Promise.all([
        this.getUserInfo(),
        this.getUserVehicles(),
        this.getFavoriteGraveSites()
      ])

      this.setData({
        userInfo,
        vehicles,
        favoriteGraveSites,
        loading: false
      })
    } catch (error) {
      console.error('加载用户数据失败:', error)
      app.utils.showError('加载数据失败')
      this.setData({ loading: false })
    }
  },

  // 获取用户信息
  getUserInfo() {
    return app.utils.request({
      url: '/api/user/profile',
      method: 'GET'
    }).then(res => res.data)
  },

  // 获取用户车辆列表
  getUserVehicles() {
    return app.utils.request({
      url: '/api/user/vehicles',
      method: 'GET'
    }).then(res => res.data)
  },

  // 获取收藏的墓位
  getFavoriteGraveSites() {
    return app.utils.request({
      url: '/api/grave-site/favorites',
      method: 'GET'
    }).then(res => res.data)
  },

  // 显示编辑用户信息弹窗
  showEditUserInfo() {
    this.setData({
      showEditModal: true,
      editForm: {
        nickname: this.data.userInfo.nickname || '',
        phone: this.data.userInfo.phone || ''
      }
    })
  },

  // 隐藏编辑弹窗
  hideEditModal() {
    this.setData({ showEditModal: false })
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    this.setData({
      [`editForm.${field}`]: value
    })
  },

  // 保存用户信息
  async saveUserInfo() {
    const { nickname, phone } = this.data.editForm

    if (!nickname.trim()) {
      app.utils.showError('请输入昵称')
      return
    }

    if (phone && !app.utils.validatePhone(phone)) {
      app.utils.showError('手机号格式不正确')
      return
    }

    try {
      app.utils.showLoading('保存中...')

      const result = await app.utils.request({
        url: '/api/user/profile',
        method: 'PUT',
        data: {
          nickname: nickname.trim(),
          phone: phone.trim()
        }
      })

      this.setData({
        userInfo: result.data,
        showEditModal: false
      })

      app.utils.showSuccess('保存成功')
    } catch (error) {
      console.error('保存用户信息失败:', error)
      app.utils.showError('保存失败')
    } finally {
      app.utils.hideLoading()
    }
  },

  // 导航到车辆管理
  navigateToVehicles() {
    wx.navigateTo({
      url: '/pages/profile/vehicles/vehicles'
    })
  },

  // 导航到墓位收藏
  navigateToFavorites() {
    wx.navigateTo({
      url: '/pages/profile/favorites/favorites'
    })
  },

  // 导航到设置页面
  navigateToSettings() {
    wx.navigateTo({
      url: '/pages/profile/settings/settings'
    })
  },

  // 查看墓位详情
  viewGraveSiteDetail(e) {
    const { graveSite } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/memorial/detail/detail?id=${graveSite.id}`
    })
  },

  // 查看车辆详情
  viewVehicleDetail(e) {
    const { vehicle } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/profile/vehicle-detail/vehicle-detail?id=${vehicle.id}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadUserData().finally(() => {
      wx.stopPullDownRefresh()
    })
  }
})