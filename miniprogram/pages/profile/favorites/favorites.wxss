/* pages/profile/favorites/favorites.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 30rpx;
  color: #666;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  text-align: center;
  padding: 100rpx 60rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.empty-btn {
  display: inline-block;
  padding: 24rpx 48rpx;
  background: #2c5aa0;
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
}

/* 收藏列表 */
.favorites-list {
  padding: 20rpx;
}

.favorite-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 墓位信息 */
.grave-info {
  padding: 30rpx;
}

.grave-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.grave-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.favorite-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ff4757;
  font-size: 32rpx;
}

.grave-location {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  color: #666;
  font-size: 28rpx;
}

.grave-location .iconfont {
  margin-right: 10rpx;
  font-size: 24rpx;
}

.grave-details {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.grave-type {
  padding: 8rpx 16rpx;
  background: #e3f2fd;
  color: #2c5aa0;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.grave-relationship {
  padding: 8rpx 16rpx;
  background: #f0f0f0;
  color: #666;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.grave-desc {
  color: #666;
  font-size: 28rpx;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.grave-date {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 26rpx;
}

.grave-date .iconfont {
  margin-right: 10rpx;
  font-size: 24rpx;
}

/* 操作按钮 */
.grave-actions {
  padding: 0 30rpx 30rpx;
}

.action-btn {
  text-align: center;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.action-btn.primary {
  background: #2c5aa0;
  color: white;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
}
