<!--pages/profile/favorites/favorites.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="page-title">收藏墓位</view>
    <view class="search-btn" bindtap="searchGraveSite">
      <text class="iconfont icon-search"></text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{favoriteGraveSites.length === 0}}" class="empty-container">
    <image class="empty-image" src="/images/empty-favorites.png" mode="aspectFit"></image>
    <view class="empty-text">暂无收藏墓位</view>
    <view class="empty-desc">您可以通过搜索或浏览墓园来收藏墓位</view>
    <view class="empty-btn" bindtap="searchGraveSite">搜索墓位</view>
  </view>

  <!-- 收藏列表 -->
  <view wx:else class="favorites-list">
    <view wx:for="{{favoriteGraveSites}}" wx:key="id" class="favorite-item"
          data-grave-site="{{item}}" bindtap="viewGraveSiteDetail">
      
      <!-- 墓位信息 -->
      <view class="grave-info">
        <view class="grave-header">
          <view class="grave-name">{{item.deceasedName}}</view>
          <view class="favorite-btn" 
                data-grave-site-id="{{item.id}}" 
                bindtap="removeFavorite">
            <text class="iconfont icon-heart-filled"></text>
          </view>
        </view>
        
        <view class="grave-location">
          <text class="iconfont icon-location"></text>
          <text>{{item.cemeteryName}} {{item.sectionName}} {{item.siteNumber}}</text>
        </view>
        
        <view class="grave-details">
          <view class="grave-type">{{item.siteType === 'single' ? '单人墓' : item.siteType === 'double' ? '双人墓' : '家族墓'}}</view>
          <view class="grave-relationship">{{item.relationship}}</view>
        </view>
        
        <view wx:if="{{item.deceasedInfo}}" class="grave-desc">{{item.deceasedInfo}}</view>
        
        <view wx:if="{{item.burialDate}}" class="grave-date">
          <text class="iconfont icon-calendar"></text>
          <text>安葬日期：{{item.burialDate}}</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="grave-actions">
        <view class="action-btn primary">查看详情</view>
      </view>
    </view>
  </view>
</view>
