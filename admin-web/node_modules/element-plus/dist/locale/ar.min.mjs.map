{"version": 3, "file": "ar.min.mjs", "sources": ["../../../../packages/locale/lang/ar.ts"], "sourcesContent": ["export default {\n  name: 'ar',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'موافق',\n      clear: 'إزالة',\n      defaultLabel: 'إختر اللون',\n      description: 'اللون الحالي هو {color}. اضفط انتر لاختيار لون جديد',\n    },\n    datepicker: {\n      now: 'الآن',\n      today: 'اليوم',\n      cancel: 'إلغاء',\n      clear: 'إزالة',\n      confirm: 'موافق',\n      dateTablePrompt:\n        'استخدم مفاتيح الاسهم و اضغط انتر لاختيار اليوم المراد من الشهر',\n      monthTablePrompt: 'استخدم مفاتيح الاسهم واضغط انتر لاختيار الشهر',\n      yearTablePrompt: 'استخدم مفاتيح الاسهم واضغط انتر لاختيار السنة',\n      selectDate: 'إختر التاريخ',\n      selectTime: 'إختر الوقت',\n      startDate: 'تاريخ البدء',\n      startTime: 'وقت البدء',\n      endDate: 'تاريخ الإنتهاء',\n      endTime: 'وقت الإنتهاء',\n      prevYear: 'السنة السابقة',\n      nextYear: 'السنة التالية',\n      prevMonth: 'الشهر السابق',\n      nextMonth: 'الشهر التالي',\n      year: 'سنة',\n      month1: 'كانون الثاني',\n      month2: 'شباط',\n      month3: 'اذار',\n      month4: 'نيسان',\n      month5: 'أيار',\n      month6: 'حزيران',\n      month7: 'تموز',\n      month8: 'اّب',\n      month9: 'ايلول',\n      month10: 'تشرين الاول',\n      month11: 'تشرين الثاني',\n      month12: 'كانون الاول',\n      week: 'أسبوع',\n      weeks: {\n        sun: 'الأحد',\n        mon: 'الأثنين',\n        tue: 'الثلاثاء',\n        wed: 'الأربعاء',\n        thu: 'الخميس',\n        fri: 'الجمعة',\n        sat: 'السبت',\n      },\n      months: {\n        jan: 'كانون الثاني',\n        feb: 'شباط',\n        mar: 'اذار',\n        apr: 'نيسان',\n        may: 'ايار',\n        jun: 'حزيران',\n        jul: 'تمور',\n        aug: 'اّب',\n        sep: 'ايلول',\n        oct: 'تشرين الاول',\n        nov: 'تشرين الثاني',\n        dec: 'كانون الاول',\n      },\n    },\n    inputNumber: {\n      decrease: 'طرح رقم',\n      increase: 'زيادة رقم',\n    },\n    select: {\n      loading: 'جار التحميل',\n      noMatch: 'لايوجد بيانات مطابقة',\n      noData: 'لايوجد بيانات',\n      placeholder: 'إختر',\n    },\n    mention: {\n      loading: 'جار التحميل',\n    },\n    dropdown: {\n      toggleDropdown: 'تبديل القائمة',\n    },\n    cascader: {\n      noMatch: 'لايوجد بيانات مطابقة',\n      loading: 'جار التحميل',\n      placeholder: 'إختر',\n      noData: 'لايوجد بيانات',\n    },\n    pagination: {\n      goto: 'أذهب إلى',\n      pagesize: '/صفحة',\n      total: 'الكل {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    dialog: {\n      close: 'أغلق هذا التبويب',\n    },\n    drawer: {\n      close: 'أغلق هذا التبويب',\n    },\n    messagebox: {\n      title: 'العنوان',\n      confirm: 'موافق',\n      cancel: 'إلغاء',\n      error: 'مدخل غير صحيح',\n      close: 'أغلق هذا التبويب',\n    },\n    upload: {\n      deleteTip: 'اضغط ازالة لحذف المحتوى',\n      delete: 'حذف',\n      preview: 'عرض',\n      continue: 'إستمرار',\n    },\n    table: {\n      emptyText: 'لايوجد بيانات',\n      confirmFilter: 'تأكيد',\n      resetFilter: 'حذف',\n      clearFilter: 'الكل',\n      sumText: 'المجموع',\n    },\n    tree: {\n      emptyText: 'لايوجد بيانات',\n    },\n    transfer: {\n      noMatch: 'لايوجد بيانات مطابقة',\n      noData: 'لايوجد بيانات',\n      titles: ['قائمة 1', 'قائمة 2'],\n      filterPlaceholder: 'ادخل كلمة',\n      noCheckedFormat: '{total} عناصر',\n      hasCheckedFormat: '{checked}/{total} مختار',\n    },\n    image: {\n      error: 'فشل',\n    },\n    pageHeader: {\n      title: 'عودة',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,KAAK,CAAC,gCAAgC,CAAC,YAAY,CAAC,yDAAyD,CAAC,WAAW,CAAC,oOAAoO,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,0BAA0B,CAAC,KAAK,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,gCAAgC,CAAC,eAAe,CAAC,oUAAoU,CAAC,gBAAgB,CAAC,kPAAkP,CAAC,eAAe,CAAC,kPAAkP,CAAC,UAAU,CAAC,qEAAqE,CAAC,UAAU,CAAC,yDAAyD,CAAC,SAAS,CAAC,+DAA+D,CAAC,SAAS,CAAC,mDAAmD,CAAC,OAAO,CAAC,iFAAiF,CAAC,OAAO,CAAC,qEAAqE,CAAC,QAAQ,CAAC,2EAA2E,CAAC,QAAQ,CAAC,2EAA2E,CAAC,SAAS,CAAC,qEAAqE,CAAC,SAAS,CAAC,qEAAqE,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,qEAAqE,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,gCAAgC,CAAC,OAAO,CAAC,+DAA+D,CAAC,OAAO,CAAC,qEAAqE,CAAC,OAAO,CAAC,+DAA+D,CAAC,IAAI,CAAC,gCAAgC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,qEAAqE,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,+DAA+D,CAAC,GAAG,CAAC,qEAAqE,CAAC,GAAG,CAAC,+DAA+D,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,uCAAuC,CAAC,QAAQ,CAAC,mDAAmD,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,+DAA+D,CAAC,OAAO,CAAC,gHAAgH,CAAC,MAAM,CAAC,2EAA2E,CAAC,WAAW,CAAC,0BAA0B,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,+DAA+D,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,2EAA2E,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gHAAgH,CAAC,OAAO,CAAC,+DAA+D,CAAC,WAAW,CAAC,0BAA0B,CAAC,MAAM,CAAC,2EAA2E,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,6CAA6C,CAAC,QAAQ,CAAC,2BAA2B,CAAC,KAAK,CAAC,kCAAkC,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,wFAAwF,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,wFAAwF,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,4CAA4C,CAAC,OAAO,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,KAAK,CAAC,sEAAsE,CAAC,KAAK,CAAC,wFAAwF,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6HAA6H,CAAC,MAAM,CAAC,oBAAoB,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,4CAA4C,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,2EAA2E,CAAC,aAAa,CAAC,gCAAgC,CAAC,WAAW,CAAC,oBAAoB,CAAC,WAAW,CAAC,0BAA0B,CAAC,OAAO,CAAC,4CAA4C,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,2EAA2E,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gHAAgH,CAAC,MAAM,CAAC,2EAA2E,CAAC,MAAM,CAAC,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC,iBAAiB,CAAC,mDAAmD,CAAC,eAAe,CAAC,wCAAwC,CAAC,gBAAgB,CAAC,kDAAkD,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}