{"version": 3, "file": "ko.min.js", "sources": ["../../../../packages/locale/lang/ko.ts"], "sourcesContent": ["export default {\n  name: 'ko',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '확인',\n      clear: '초기화',\n      defaultLabel: '색상 선택기',\n      description:\n        '현재 색상은 {color}입니다. Enter 키를 눌러 새 색상을 선택합니다.',\n    },\n    datepicker: {\n      now: '지금',\n      today: '오늘',\n      cancel: '취소',\n      clear: '초기화',\n      confirm: '확인',\n      dateTablePrompt: '화살표 키를 사용하고 Enter를 눌러 날짜를 선택하십시오.',\n      monthTablePrompt: '화살표 키를 사용하고 Enter를 눌러 월을 선택합니다.',\n      yearTablePrompt:\n        '화살표 키를 사용하고 Enter 키를 눌러 연도를 선택합니다.',\n      selectDate: '날짜 선택',\n      selectTime: '시간 선택',\n      startDate: '시작 날짜',\n      startTime: '시작 시간',\n      endDate: '종료 날짜',\n      endTime: '종료 시간',\n      prevYear: '지난해',\n      nextYear: '다음해',\n      prevMonth: '지난달',\n      nextMonth: '다음달',\n      year: '년',\n      month1: '1월',\n      month2: '2월',\n      month3: '3월',\n      month4: '4월',\n      month5: '5월',\n      month6: '6월',\n      month7: '7월',\n      month8: '8월',\n      month9: '9월',\n      month10: '10월',\n      month11: '11월',\n      month12: '12월',\n      // week: 'week',\n      weeks: {\n        sun: '일',\n        mon: '월',\n        tue: '화',\n        wed: '수',\n        thu: '목',\n        fri: '금',\n        sat: '토',\n      },\n      months: {\n        jan: '1월',\n        feb: '2월',\n        mar: '3월',\n        apr: '4월',\n        may: '5월',\n        jun: '6월',\n        jul: '7월',\n        aug: '8월',\n        sep: '9월',\n        oct: '10월',\n        nov: '11월',\n        dec: '12월',\n      },\n    },\n    inputNumber: {\n      decrease: '값 증가',\n      increase: '값 감소',\n    },\n    select: {\n      loading: '불러오는 중',\n      noMatch: '검색된 데이터 없음',\n      noData: '데이터 없음',\n      placeholder: '선택',\n    },\n    mention: {\n      loading: '불러오는 중',\n    },\n    dropdown: {\n      toggleDropdown: '드롭다운 전환',\n    },\n    cascader: {\n      noMatch: '검색된 데이터 없음',\n      loading: '불러오는 중',\n      placeholder: '선택',\n      noData: '데이터 없음',\n    },\n    pagination: {\n      goto: '이동',\n      pagesize: '건/페이지',\n      total: '총 {total} 건',\n      pageClassifier: '페이지로',\n      page: '페이지',\n      prev: '이전 페이지로 이동',\n      next: '다음 페이지로 이동',\n      currentPage: '페이지 {pager}',\n      prevPages: '이전 {pager} 페이지',\n      nextPages: '다음 {pager} 페이지',\n      deprecationWarning:\n        '더 이상 사용되지 않는 동작이 감지되었습니다. 자세한 내용은 el-pagination 문서를 참조하세요.',\n    },\n    dialog: {\n      close: '대화 상자 닫기',\n    },\n    drawer: {\n      close: '대화 상자 닫기',\n    },\n    messagebox: {\n      title: '메시지',\n      confirm: '확인',\n      cancel: '취소',\n      error: '올바르지 않은 입력',\n      close: '대화 상자 닫기',\n    },\n    upload: {\n      deleteTip: 'Delete 키를 눌러 삭제',\n      delete: '삭제',\n      preview: '미리보기',\n      continue: '계속하기',\n    },\n    slider: {\n      defaultLabel: '{min}과 {max} 사이의 슬라이더',\n      defaultRangeStartLabel: '시작 값 선택',\n      defaultRangeEndLabel: '종료 값 선택',\n    },\n    table: {\n      emptyText: '데이터 없음',\n      confirmFilter: '확인',\n      resetFilter: '초기화',\n      clearFilter: '전체',\n      sumText: '합계',\n    },\n    tour: {\n      next: '다음',\n      previous: '이전',\n      finish: '종료',\n    },\n    tree: {\n      emptyText: '데이터 없음',\n    },\n    transfer: {\n      noMatch: '검색된 데이터 없음',\n      noData: '데이터 없음',\n      titles: ['리스트 1', '리스트 2'],\n      filterPlaceholder: '검색어를 입력하세요',\n      noCheckedFormat: '총 {total} 건',\n      hasCheckedFormat: '{checked}/{total} 선택됨',\n    },\n    image: {\n      error: '불러오기 실패',\n    },\n    pageHeader: {\n      title: '뒤로',\n    },\n    popconfirm: {\n      confirmButtonText: '예',\n      cancelButtonText: '아니오',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,oBAAoB,CAAC,YAAY,CAAC,iCAAiC,CAAC,WAAW,CAAC,sJAAsJ,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,4IAA4I,CAAC,gBAAgB,CAAC,gIAAgI,CAAC,eAAe,CAAC,6IAA6I,CAAC,UAAU,CAAC,2BAA2B,CAAC,UAAU,CAAC,2BAA2B,CAAC,SAAS,CAAC,2BAA2B,CAAC,SAAS,CAAC,2BAA2B,CAAC,OAAO,CAAC,2BAA2B,CAAC,OAAO,CAAC,2BAA2B,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ,CAAC,oBAAoB,CAAC,SAAS,CAAC,oBAAoB,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,OAAO,CAAC,oDAAoD,CAAC,MAAM,CAAC,iCAAiC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,uCAAuC,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,oDAAoD,CAAC,OAAO,CAAC,iCAAiC,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,CAAC,iCAAiC,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,2BAA2B,CAAC,KAAK,CAAC,uBAAuB,CAAC,cAAc,CAAC,0BAA0B,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,oDAAoD,CAAC,IAAI,CAAC,oDAAoD,CAAC,WAAW,CAAC,4BAA4B,CAAC,SAAS,CAAC,yCAAyC,CAAC,SAAS,CAAC,yCAAyC,CAAC,kBAAkB,CAAC,iOAAiO,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,oDAAoD,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,+CAA+C,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,0BAA0B,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,+DAA+D,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,oBAAoB,CAAC,kCAAkC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,aAAa,CAAC,cAAc,CAAC,WAAW,CAAC,oBAAoB,CAAC,WAAW,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,iCAAiC,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,oDAAoD,CAAC,MAAM,CAAC,iCAAiC,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC,iBAAiB,CAAC,yDAAyD,CAAC,eAAe,CAAC,uBAAuB,CAAC,gBAAgB,CAAC,sCAAsC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}