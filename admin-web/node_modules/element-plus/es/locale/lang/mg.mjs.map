{"version": 3, "file": "mg.mjs", "sources": ["../../../../../packages/locale/lang/mg.ts"], "sourcesContent": ["export default {\n  name: 'mg',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'EN<PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>',\n      today: '<PERSON><PERSON>ny',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: 'ENY',\n      selectDate: 'Misafidy daty',\n      selectTime: 'Misafidy ora',\n      startDate: '<PERSON><PERSON> fanombohana',\n      startTime: '<PERSON>a fanombohana',\n      endDate: '<PERSON>ty farany',\n      endTime: 'Ora farany',\n      prevYear: 'Taona teo aloha',\n      nextYear: '<PERSON>na manaraka',\n      prevMonth: '<PERSON>ana teo aloha',\n      nextMonth: '<PERSON>ana manaraka',\n      year: '',\n      month1: 'Janoary',\n      month2: 'Febroary',\n      month3: 'Martsa',\n      month4: 'Aprily',\n      month5: 'May',\n      month6: 'Jona',\n      month7: 'Jolay',\n      month8: 'A<PERSON><PERSON><PERSON>',\n      month9: 'Septambra',\n      month10: 'Ok<PERSON><PERSON>',\n      month11: 'Nova<PERSON><PERSON>',\n      month12: '<PERSON><PERSON><PERSON>',\n      week: 'herinandro',\n      weeks: {\n        sun: 'Lad',\n        mon: 'Ala',\n        tue: 'Tal',\n        wed: 'Lar',\n        thu: 'Lak',\n        fri: 'Zom',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'May',\n        jun: 'Jon',\n        jul: 'Jol',\n        aug: 'Aog',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Des',\n      },\n    },\n    select: {\n      loading: 'Eo ampiandrasana',\n      noMatch: 'Tsy misy angona mifanentana',\n      noData: 'Tsy misy angona',\n      placeholder: 'Safidy',\n    },\n    mention: {\n      loading: 'Eo ampiandrasana',\n    },\n    cascader: {\n      noMatch: 'Tsy misy angona mifanentana',\n      loading: 'Eo ampiandrasana',\n      placeholder: 'Safidy',\n      noData: 'Tsy misy angona',\n    },\n    pagination: {\n      goto: 'Mandeha any',\n      pagesize: '/page',\n      total: 'Totaly {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n      deprecationWarning:\n        'Fampiasana tsy ampiasaina intsony no hita, azafady mba jereo ny tahirin-kevitra el-pagination raha mila fanazavana fanampiny',\n    },\n    messagebox: {\n      title: 'Hafatra',\n      confirm: 'ENY',\n      cancel: 'Hanafoana',\n      error: 'Fampidirana tsy ara-dalàna',\n    },\n    upload: {\n      deleteTip: 'tsindrio fafana raha hanala',\n      delete: 'Fafana',\n      preview: 'Topi-maso',\n      continue: 'Hanoy',\n    },\n    table: {\n      emptyText: 'Tsy misy angona',\n      confirmFilter: 'Manamarina',\n      resetFilter: 'Averina',\n      clearFilter: 'Rehetra',\n      sumText: 'Atambatra',\n    },\n    tree: {\n      emptyText: 'Tsy misy angona',\n    },\n    transfer: {\n      noMatch: 'Tsy misy angona mifanentana',\n      noData: 'Tsy misy angona',\n      titles: ['Lisitra 1', 'Lisitra 2'], // to be translated\n      filterPlaceholder: 'Ampidiro teny fanalahidy', // to be translated\n      noCheckedFormat: '{total} zavatra', // to be translated\n      hasCheckedFormat: '{checked}/{total} voamarina', // to be translated\n    },\n    image: {\n      error: 'TSY NAHOMBY',\n    },\n    pageHeader: {\n      title: 'Miverina', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Eny',\n      cancelButtonText: 'Tsy',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,UAAU,EAAE,eAAe;AACjC,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,SAAS,EAAE,kBAAkB;AACnC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,WAAW,EAAE,QAAQ;AAC3B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,kBAAkB;AACjC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,MAAM,EAAE,iBAAiB;AAC/B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,kBAAkB,EAAE,8HAA8H;AACxJ,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,KAAK,EAAE,+BAA+B;AAC5C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,6BAA6B;AAC9C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,OAAO;AACvB,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,aAAa,EAAE,YAAY;AACjC,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,WAAW,EAAE,SAAS;AAC5B,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,iBAAiB;AAClC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,6BAA6B;AAC5C,MAAM,MAAM,EAAE,iBAAiB;AAC/B,MAAM,MAAM,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;AACxC,MAAM,iBAAiB,EAAE,0BAA0B;AACnD,MAAM,eAAe,EAAE,iBAAiB;AACxC,MAAM,gBAAgB,EAAE,6BAA6B;AACrD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,aAAa;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,UAAU;AACvB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,KAAK;AAC7B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}