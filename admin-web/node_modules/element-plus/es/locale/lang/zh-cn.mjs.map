{"version": 3, "file": "zh-cn.mjs", "sources": ["../../../../../packages/locale/lang/zh-cn.ts"], "sourcesContent": ["export default {\n  name: 'zh-cn',\n  el: {\n    breadcrumb: {\n      label: '面包屑',\n    },\n    colorpicker: {\n      confirm: '确定',\n      clear: '清空',\n      defaultLabel: '颜色选择器',\n      description: '当前颜色 {color}，按 Enter 键选择新颜色',\n      alphaLabel: '选择透明度的值',\n    },\n    datepicker: {\n      now: '此刻',\n      today: '今天',\n      cancel: '取消',\n      clear: '清空',\n      confirm: '确定',\n      dateTablePrompt: '使用方向键与 Enter 键可选择日期',\n      monthTablePrompt: '使用方向键与 Enter 键可选择月份',\n      yearTablePrompt: '使用方向键与 Enter 键可选择年份',\n      selectedDate: '已选日期',\n      selectDate: '选择日期',\n      selectTime: '选择时间',\n      startDate: '开始日期',\n      startTime: '开始时间',\n      endDate: '结束日期',\n      endTime: '结束时间',\n      prevYear: '前一年',\n      nextYear: '后一年',\n      prevMonth: '上个月',\n      nextMonth: '下个月',\n      year: '年',\n      month1: '1 月',\n      month2: '2 月',\n      month3: '3 月',\n      month4: '4 月',\n      month5: '5 月',\n      month6: '6 月',\n      month7: '7 月',\n      month8: '8 月',\n      month9: '9 月',\n      month10: '10 月',\n      month11: '11 月',\n      month12: '12 月',\n      // week: '周次',\n      weeks: {\n        sun: '日',\n        mon: '一',\n        tue: '二',\n        wed: '三',\n        thu: '四',\n        fri: '五',\n        sat: '六',\n      },\n      weeksFull: {\n        sun: '星期日',\n        mon: '星期一',\n        tue: '星期二',\n        wed: '星期三',\n        thu: '星期四',\n        fri: '星期五',\n        sat: '星期六',\n      },\n      months: {\n        jan: '一月',\n        feb: '二月',\n        mar: '三月',\n        apr: '四月',\n        may: '五月',\n        jun: '六月',\n        jul: '七月',\n        aug: '八月',\n        sep: '九月',\n        oct: '十月',\n        nov: '十一月',\n        dec: '十二月',\n      },\n    },\n    inputNumber: {\n      decrease: '减少数值',\n      increase: '增加数值',\n    },\n    select: {\n      loading: '加载中',\n      noMatch: '无匹配数据',\n      noData: '无数据',\n      placeholder: '请选择',\n    },\n    dropdown: {\n      toggleDropdown: '切换下拉选项',\n    },\n    mention: {\n      loading: '加载中',\n    },\n    cascader: {\n      noMatch: '无匹配数据',\n      loading: '加载中',\n      placeholder: '请选择',\n      noData: '暂无数据',\n    },\n    pagination: {\n      goto: '前往',\n      pagesize: '条/页',\n      total: '共 {total} 条',\n      pageClassifier: '页',\n      page: '页',\n      prev: '上一页',\n      next: '下一页',\n      currentPage: '第 {pager} 页',\n      prevPages: '向前 {pager} 页',\n      nextPages: '向后 {pager} 页',\n      deprecationWarning:\n        '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档',\n    },\n    dialog: {\n      close: '关闭此对话框',\n    },\n    drawer: {\n      close: '关闭此对话框',\n    },\n    messagebox: {\n      title: '提示',\n      confirm: '确定',\n      cancel: '取消',\n      error: '输入的数据不合法!',\n      close: '关闭此对话框',\n    },\n    upload: {\n      deleteTip: '按 Delete 键可删除',\n      delete: '删除',\n      preview: '查看图片',\n      continue: '继续上传',\n    },\n    slider: {\n      defaultLabel: '滑块介于 {min} 至 {max}',\n      defaultRangeStartLabel: '选择起始值',\n      defaultRangeEndLabel: '选择结束值',\n    },\n    table: {\n      emptyText: '暂无数据',\n      confirmFilter: '筛选',\n      resetFilter: '重置',\n      clearFilter: '全部',\n      sumText: '合计',\n    },\n    tour: {\n      next: '下一步',\n      previous: '上一步',\n      finish: '结束导览',\n    },\n    tree: {\n      emptyText: '暂无数据',\n    },\n    transfer: {\n      noMatch: '无匹配数据',\n      noData: '无数据',\n      titles: ['列表 1', '列表 2'],\n      filterPlaceholder: '请输入搜索内容',\n      noCheckedFormat: '共 {total} 项',\n      hasCheckedFormat: '已选 {checked}/{total} 项',\n    },\n    image: {\n      error: '加载失败',\n    },\n    pageHeader: {\n      title: '返回',\n    },\n    popconfirm: {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n    },\n    carousel: {\n      leftArrow: '上一张幻灯片',\n      rightArrow: '下一张幻灯片',\n      indicator: '幻灯片切换至索引 {index}',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,WAAe;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,oBAAoB;AACjC,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,YAAY,EAAE,gCAAgC;AACpD,MAAM,WAAW,EAAE,yFAAyF;AAC5G,MAAM,UAAU,EAAE,4CAA4C;AAC9D,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,cAAc;AACzB,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,eAAe,EAAE,iFAAiF;AACxG,MAAM,gBAAgB,EAAE,iFAAiF;AACzG,MAAM,eAAe,EAAE,iFAAiF;AACxG,MAAM,YAAY,EAAE,0BAA0B;AAC9C,MAAM,UAAU,EAAE,0BAA0B;AAC5C,MAAM,UAAU,EAAE,0BAA0B;AAC5C,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,QAAQ,EAAE,oBAAoB;AACpC,MAAM,QAAQ,EAAE,oBAAoB;AACpC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,QAAQ,GAAG,EAAE,QAAQ;AACrB,OAAO;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,WAAW,EAAE,oBAAoB;AACvC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,cAAc,EAAE,sCAAsC;AAC5D,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,oBAAoB;AACnC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,MAAM,EAAE,0BAA0B;AACxC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,QAAQ,EAAE,eAAe;AAC/B,MAAM,KAAK,EAAE,uBAAuB;AACpC,MAAM,cAAc,EAAE,QAAQ;AAC9B,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,WAAW,EAAE,uBAAuB;AAC1C,MAAM,SAAS,EAAE,6BAA6B;AAC9C,MAAM,SAAS,EAAE,6BAA6B;AAC9C,MAAM,kBAAkB,EAAE,qJAAqJ;AAC/K,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,sCAAsC;AACnD,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,sCAAsC;AACnD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,KAAK,EAAE,mDAAmD;AAChE,MAAM,KAAK,EAAE,sCAAsC;AACnD,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wCAAwC;AACzD,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,YAAY,EAAE,6CAA6C;AACjE,MAAM,sBAAsB,EAAE,gCAAgC;AAC9D,MAAM,oBAAoB,EAAE,gCAAgC;AAC5D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,aAAa,EAAE,cAAc;AACnC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,OAAO,EAAE,cAAc;AAC7B,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,QAAQ,EAAE,oBAAoB;AACpC,MAAM,MAAM,EAAE,0BAA0B;AACxC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,0BAA0B;AAC3C,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gCAAgC;AAC/C,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;AAClD,MAAM,iBAAiB,EAAE,4CAA4C;AACrE,MAAM,eAAe,EAAE,uBAAuB;AAC9C,MAAM,gBAAgB,EAAE,uCAAuC;AAC/D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,0BAA0B;AACvC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,cAAc;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,cAAc;AACvC,MAAM,gBAAgB,EAAE,cAAc;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,sCAAsC;AACvD,MAAM,UAAU,EAAE,sCAAsC;AACxD,MAAM,SAAS,EAAE,0DAA0D;AAC3E,KAAK;AACL,GAAG;AACH,CAAC;;;;"}