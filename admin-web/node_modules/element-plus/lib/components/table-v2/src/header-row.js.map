{"version": 3, "file": "header-row.js", "sources": ["../../../../../../packages/components/table-v2/src/header-row.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { columns } from './common'\n\nimport type {\n  CSSProperties,\n  ExtractPropTypes,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type { KeyType } from './types'\n\nexport const tableV2HeaderRowProps = buildProps({\n  class: String,\n  columns,\n  columnsStyles: {\n    type: definePropType<Record<KeyType, CSSProperties>>(Object),\n    required: true,\n  },\n  headerIndex: Number,\n  style: { type: definePropType<CSSProperties>(Object) },\n} as const)\n\nexport type TableV2HeaderRowProps = ExtractPropTypes<\n  typeof tableV2HeaderRowProps\n>\nexport type TableV2HeaderRowPropsPublic = __ExtractPublicPropTypes<\n  typeof tableV2HeaderRowProps\n>\n"], "names": ["buildProps", "columns", "definePropType"], "mappings": ";;;;;;;AAEY,MAAC,qBAAqB,GAAGA,kBAAU,CAAC;AAChD,EAAE,KAAK,EAAE,MAAM;AACf,WAAEC,cAAO;AACT,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,KAAK,EAAE,EAAE,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC,EAAE;AACzC,CAAC;;;;"}