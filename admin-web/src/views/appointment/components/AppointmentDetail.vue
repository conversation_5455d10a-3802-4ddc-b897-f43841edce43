<template>
  <el-dialog
    v-model="dialogVisible"
    title="预约详情"
    width="600px"
    @close="handleClose"
  >
    <div v-if="appointment" class="appointment-detail">
      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="预约ID">{{ appointment.id }}</el-descriptions-item>
        <el-descriptions-item label="预约状态">
          <el-tag :type="getStatusType(appointment.status)">
            {{ appointment.statusDescription }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="预约日期">{{ appointment.appointmentDate }}</el-descriptions-item>
        <el-descriptions-item label="预约时段">{{ appointment.timeSlot }}</el-descriptions-item>
        <el-descriptions-item label="时段描述">{{ appointment.timeSlotDescription }}</el-descriptions-item>
        <el-descriptions-item label="同行人数">{{ appointment.companions }}人</el-descriptions-item>
      </el-descriptions>

      <!-- 联系信息 -->
      <el-descriptions title="联系信息" :column="2" border style="margin-top: 20px;">
        <el-descriptions-item label="联系人">{{ appointment.contactName }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ appointment.contactPhone }}</el-descriptions-item>
      </el-descriptions>

      <!-- 墓园信息 -->
      <el-descriptions title="墓园信息" :column="2" border style="margin-top: 20px;" v-if="appointment.cemeteryName">
        <el-descriptions-item label="墓园名称">{{ appointment.cemeteryName }}</el-descriptions-item>
        <el-descriptions-item label="墓位编号">{{ appointment.graveSiteNumber || '未指定' }}</el-descriptions-item>
      </el-descriptions>

      <!-- 特殊需求 -->
      <el-descriptions title="特殊需求" :column="1" border style="margin-top: 20px;" v-if="appointment.specialNeeds">
        <el-descriptions-item label="需求描述">
          <div class="special-needs">{{ appointment.specialNeeds }}</div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 二维码 -->
      <el-descriptions title="预约凭证" :column="1" border style="margin-top: 20px;" v-if="appointment.qrCode">
        <el-descriptions-item label="二维码">
          <div class="qr-code">
            <el-input v-model="appointment.qrCode" readonly />
            <el-button type="primary" size="small" @click="generateQrCode" style="margin-left: 10px;">
              重新生成
            </el-button>
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 时间信息 -->
      <el-descriptions title="时间信息" :column="2" border style="margin-top: 20px;">
        <el-descriptions-item label="创建时间">{{ appointment.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ appointment.updatedAt }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="appointment?.status === 'pending'"
          type="success"
          @click="confirmAppointment"
        >
          确认预约
        </el-button>
        <el-button
          v-if="appointment?.status === 'confirmed'"
          type="info"
          @click="completeAppointment"
        >
          标记完成
        </el-button>
        <el-button
          v-if="['pending', 'confirmed'].includes(appointment?.status || '')"
          type="danger"
          @click="cancelAppointment"
        >
          取消预约
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { AppointmentInfo } from '@/api/appointment'

interface Props {
  visible: boolean
  appointment: AppointmentInfo | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    pending: 'warning',
    confirmed: 'success',
    completed: 'info',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 确认预约
const confirmAppointment = async () => {
  if (!props.appointment) return

  try {
    await ElMessageBox.confirm('确定要确认这个预约吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await appointmentAPI.confirmAppointment(props.appointment.id)
    ElMessage.success('预约确认成功')
    emit('refresh')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('确认预约失败:', error)
      ElMessage.error('确认预约失败')
    }
  }
}

// 完成预约
const completeAppointment = async () => {
  if (!props.appointment) return

  try {
    await ElMessageBox.confirm('确定要标记这个预约为已完成吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await appointmentAPI.completeAppointment(props.appointment.id)
    ElMessage.success('预约已标记为完成')
    emit('refresh')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成预约失败:', error)
      ElMessage.error('完成预约失败')
    }
  }
}

// 取消预约
const cancelAppointment = async () => {
  if (!props.appointment) return

  try {
    const { value: reason } = await ElMessageBox.prompt('请输入取消原因', '取消预约', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入取消原因'
    })

    await appointmentAPI.cancelAppointment(props.appointment.id, reason)
    ElMessage.success('预约取消成功')
    emit('refresh')
    handleClose()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消预约失败:', error)
      ElMessage.error('取消预约失败')
    }
  }
}

// 重新生成二维码
const generateQrCode = () => {
  ElMessage.info('重新生成二维码功能开发中...')
}
</script>

<style scoped>
.appointment-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.special-needs {
  white-space: pre-wrap;
  line-height: 1.5;
}

.qr-code {
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
