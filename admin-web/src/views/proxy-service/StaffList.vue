<template>
  <div class="proxy-staff-list">
    <div class="header">
      <h2>代客服务人员管理</h2>
      <el-button type="primary" @click="showAddDialog">
        <el-icon><Plus /></el-icon>
        新增人员
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="在职" value="active" />
            <el-option label="离职" value="inactive" />
            <el-option label="忙碌" value="busy" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadStaff">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table :data="staffList" v-loading="loading" stripe>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="姓名" width="120" />
      <el-table-column prop="phone" label="手机号" width="130" />
      <el-table-column prop="experienceYears" label="工作经验" width="100">
        <template #default="{ row }">
          {{ row.experienceYears }}年
        </template>
      </el-table-column>
      <el-table-column prop="rating" label="评分" width="100">
        <template #default="{ row }">
          <el-rate
            v-model="row.rating"
            disabled
            show-score
            text-color="#ff9900"
            score-template="{value}"
          />
        </template>
      </el-table-column>
      <el-table-column prop="totalOrders" label="总订单数" width="100" />
      <el-table-column prop="specialties" label="专长" show-overflow-tooltip />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="入职时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="showEditDialog(row)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteStaff(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadStaff"
        @current-change="loadStaff"
      />
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="form" :rules="rules" ref="formRef" label-width="100px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="工作经验" prop="experienceYears">
          <el-input-number
            v-model="form.experienceYears"
            :min="0"
            :max="50"
            placeholder="请输入工作经验(年)"
          />
        </el-form-item>
        <el-form-item label="专长描述" prop="specialties">
          <el-input
            v-model="form.specialties"
            type="textarea"
            :rows="3"
            placeholder="请输入专长描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="active">在职</el-radio>
            <el-radio label="inactive">离职</el-radio>
            <el-radio label="busy">忙碌</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { proxyServiceApi } from '@/api/proxy-service'

// 响应式数据
const loading = ref(false)
const staffList = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  name: '',
  phone: '',
  idCard: '',
  experienceYears: 0,
  specialties: '',
  status: 'active'
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ]
}

// 方法
const loadStaff = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const response = await proxyServiceApi.getStaff(params)
    staffList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载人员列表失败')
  } finally {
    loading.value = false
  }
}

const resetSearch = () => {
  searchForm.name = ''
  searchForm.status = ''
  pagination.page = 1
  loadStaff()
}

const showAddDialog = () => {
  dialogTitle.value = '新增人员'
  isEdit.value = false
  dialogVisible.value = true
}

const showEditDialog = (row) => {
  dialogTitle.value = '编辑人员'
  isEdit.value = true
  Object.assign(form, row)
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    name: '',
    phone: '',
    idCard: '',
    experienceYears: 0,
    specialties: '',
    status: 'active'
  })
  formRef.value?.resetFields()
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      await proxyServiceApi.updateStaff(form.id, form)
      ElMessage.success('人员信息更新成功')
    } else {
      await proxyServiceApi.createStaff(form)
      ElMessage.success('人员添加成功')
    }
    
    dialogVisible.value = false
    loadStaff()
  } catch (error) {
    ElMessage.error(isEdit.value ? '人员信息更新失败' : '人员添加失败')
  }
}

const deleteStaff = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个人员吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await proxyServiceApi.deleteStaff(row.id)
    ElMessage.success('人员删除成功')
    loadStaff()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('人员删除失败')
    }
  }
}

const getStatusType = (status) => {
  const statusMap = {
    active: 'success',
    inactive: 'danger',
    busy: 'warning'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    active: '在职',
    inactive: '离职',
    busy: '忙碌'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadStaff()
})
</script>

<style scoped>
.proxy-staff-list {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
