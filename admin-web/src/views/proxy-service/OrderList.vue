<template>
  <div class="proxy-order-list">
    <div class="header">
      <h2>代客订单管理</h2>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="订单状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待确认" value="pending" />
            <el-option label="已确认" value="confirmed" />
            <el-option label="已分配" value="assigned" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="服务人员">
          <el-select v-model="searchForm.staffId" placeholder="请选择服务人员" clearable>
            <el-option
              v-for="staff in staffOptions"
              :key="staff.id"
              :label="staff.name"
              :value="staff.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预约日期">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadOrders">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ statistics.totalOrders || 0 }}</div>
              <div class="stats-label">总订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ statistics.pendingOrders || 0 }}</div>
              <div class="stats-label">待处理</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">{{ statistics.inProgressOrders || 0 }}</div>
              <div class="stats-label">进行中</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-number">¥{{ statistics.totalAmount || 0 }}</div>
              <div class="stats-label">总金额</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据表格 -->
    <el-table :data="orders" v-loading="loading" stripe>
      <el-table-column prop="orderNo" label="订单号" width="180" />
      <el-table-column prop="userName" label="用户" width="120" />
      <el-table-column prop="packageName" label="套餐" width="120" />
      <el-table-column prop="contactName" label="联系人" width="100" />
      <el-table-column prop="contactPhone" label="联系电话" width="130" />
      <el-table-column prop="appointmentDate" label="预约日期" width="120" />
      <el-table-column prop="appointmentTime" label="预约时间" width="100" />
      <el-table-column prop="staffName" label="服务人员" width="100" />
      <el-table-column prop="totalAmount" label="金额" width="100">
        <template #default="{ row }">
          ¥{{ row.totalAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="showOrderDetail(row)">详情</el-button>
          <el-button 
            v-if="row.status === 'confirmed'"
            size="small" 
            type="primary" 
            @click="showAssignDialog(row)"
          >
            分配
          </el-button>
          <el-dropdown v-if="canUpdateStatus(row.status)">
            <el-button size="small">
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="updateStatus(row, 'confirmed')" v-if="row.status === 'pending'">
                  确认订单
                </el-dropdown-item>
                <el-dropdown-item @click="updateStatus(row, 'in_progress')" v-if="row.status === 'assigned'">
                  开始服务
                </el-dropdown-item>
                <el-dropdown-item @click="updateStatus(row, 'completed')" v-if="row.status === 'in_progress'">
                  完成服务
                </el-dropdown-item>
                <el-dropdown-item @click="updateStatus(row, 'cancelled')" v-if="['pending', 'confirmed'].includes(row.status)">
                  取消订单
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadOrders"
        @current-change="loadOrders"
      />
    </div>

    <!-- 分配服务人员对话框 -->
    <el-dialog v-model="assignDialogVisible" title="分配服务人员" width="500px">
      <el-form :model="assignForm" label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="assignForm.orderNo" disabled />
        </el-form-item>
        <el-form-item label="服务人员" required>
          <el-select v-model="assignForm.staffId" placeholder="请选择服务人员">
            <el-option
              v-for="staff in activeStaff"
              :key="staff.id"
              :label="`${staff.name} (评分: ${staff.rating})`"
              :value="staff.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmAssign">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown } from '@element-plus/icons-vue'
import { proxyServiceApi } from '@/api/proxy-service'

// 响应式数据
const loading = ref(false)
const orders = ref([])
const staffOptions = ref([])
const activeStaff = ref([])
const assignDialogVisible = ref(false)
const dateRange = ref([])

// 搜索表单
const searchForm = reactive({
  status: '',
  staffId: null,
  startDate: '',
  endDate: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 统计数据
const statistics = ref({})

// 分配表单
const assignForm = reactive({
  orderId: null,
  orderNo: '',
  staffId: null
})

// 方法
const loadOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }
    const response = await proxyServiceApi.getOrders(params)
    orders.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载订单列表失败')
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await proxyServiceApi.getOrderStatistics({
      startDate: searchForm.startDate,
      endDate: searchForm.endDate
    })
    statistics.value = response.data
  } catch (error) {
    console.error('加载统计数据失败', error)
  }
}

const loadStaffOptions = async () => {
  try {
    const response = await proxyServiceApi.getStaff({ page: 1, size: 100 })
    staffOptions.value = response.data.records
  } catch (error) {
    console.error('加载服务人员失败', error)
  }
}

const loadActiveStaff = async () => {
  try {
    const response = await proxyServiceApi.getActiveStaff()
    activeStaff.value = response.data
  } catch (error) {
    console.error('加载活跃服务人员失败', error)
  }
}

const resetSearch = () => {
  Object.assign(searchForm, {
    status: '',
    staffId: null,
    startDate: '',
    endDate: ''
  })
  dateRange.value = []
  pagination.page = 1
  loadOrders()
  loadStatistics()
}

const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    searchForm.startDate = dates[0]
    searchForm.endDate = dates[1]
  } else {
    searchForm.startDate = ''
    searchForm.endDate = ''
  }
}

const showOrderDetail = (row) => {
  // 显示订单详情
  ElMessage.info('订单详情功能待实现')
}

const showAssignDialog = (row) => {
  assignForm.orderId = row.id
  assignForm.orderNo = row.orderNo
  assignForm.staffId = null
  assignDialogVisible.value = true
}

const confirmAssign = async () => {
  if (!assignForm.staffId) {
    ElMessage.warning('请选择服务人员')
    return
  }

  try {
    await proxyServiceApi.assignStaff(assignForm.orderId, assignForm.staffId)
    ElMessage.success('分配成功')
    assignDialogVisible.value = false
    loadOrders()
  } catch (error) {
    ElMessage.error('分配失败')
  }
}

const updateStatus = async (row, status) => {
  try {
    await ElMessageBox.confirm(`确定要${getStatusText(status)}吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await proxyServiceApi.updateOrderStatus(row.id, status)
    ElMessage.success('状态更新成功')
    loadOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('状态更新失败')
    }
  }
}

const canUpdateStatus = (status) => {
  return ['pending', 'confirmed', 'assigned', 'in_progress'].includes(status)
}

const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    confirmed: 'primary',
    assigned: 'info',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待确认',
    confirmed: '已确认',
    assigned: '已分配',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadOrders()
  loadStatistics()
  loadStaffOptions()
  loadActiveStaff()
})
</script>

<style scoped>
.proxy-order-list {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-bar {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  text-align: center;
}

.stats-content {
  padding: 10px;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
