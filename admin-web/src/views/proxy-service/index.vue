<template>
  <div class="proxy-service-container">
    <el-tabs v-model="activeTab" type="card" class="proxy-tabs">
      <el-tab-pane label="订单管理" name="orders">
        <OrderList />
      </el-tab-pane>
      <el-tab-pane label="套餐管理" name="packages">
        <PackageList />
      </el-tab-pane>
      <el-tab-pane label="人员管理" name="staff">
        <StaffList />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import OrderList from './OrderList.vue'
import PackageList from './PackageList.vue'
import StaffList from './StaffList.vue'

const activeTab = ref('orders')
</script>

<style scoped>
.proxy-service-container {
  padding: 20px;
}

.proxy-tabs {
  min-height: 600px;
}

.proxy-tabs :deep(.el-tab-pane) {
  padding: 0;
}
</style>
