<template>
  <el-container class="layout-container">
    <!-- 侧边栏 -->
    <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
      <div class="logo">
        <div class="logo-icon" v-if="!isCollapse">H</div>
        <span v-if="!isCollapse">哈迪斯管理后台</span>
      </div>
      
      <el-menu
        :default-active="activeMenu"
        :default-openeds="defaultOpeneds"
        :collapse="isCollapse"
        :unique-opened="true"
        router
        class="sidebar-menu"
      >
        <el-menu-item index="/dashboard">
          <el-icon><Dashboard /></el-icon>
          <template #title>仪表盘</template>
        </el-menu-item>

        <el-sub-menu index="user">
          <template #title>
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </template>
          <el-menu-item index="/user/list">用户列表</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="appointment">
          <template #title>
            <el-icon><Calendar /></el-icon>
            <span>预约管理</span>
          </template>
          <el-menu-item index="/appointment/list">预约列表</el-menu-item>
          <el-menu-item index="/appointment/calendar">预约日历</el-menu-item>
          <el-menu-item index="/appointment/stats">预约统计</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="cemetery">
          <template #title>
            <el-icon><OfficeBuilding /></el-icon>
            <span>墓园管理</span>
          </template>
          <el-menu-item index="/cemetery/list">墓园列表</el-menu-item>
          <el-menu-item index="/gravesite/list">墓位管理</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="parking">
          <template #title>
            <el-icon><Parking /></el-icon>
            <span>停车管理</span>
          </template>
          <el-menu-item index="/parking/lots">停车场管理</el-menu-item>
          <el-menu-item index="/parking/spaces">停车位管理</el-menu-item>
          <el-menu-item index="/parking/bookings">预约记录</el-menu-item>
        </el-sub-menu>

        <el-menu-item index="/proxy-service">
          <el-icon><Service /></el-icon>
          <template #title>代客服务</template>
        </el-menu-item>

        <el-sub-menu index="message">
          <template #title>
            <el-icon><Message /></el-icon>
            <span>消息管理</span>
          </template>
          <el-menu-item index="/message/list">消息列表</el-menu-item>
          <el-menu-item index="/message/send">发送消息</el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="system">
          <template #title>
            <el-icon><Setting /></el-icon>
            <span>系统管理</span>
          </template>
          <el-menu-item index="/system/admin">管理员管理</el-menu-item>
          <el-menu-item index="/system/role">角色管理</el-menu-item>
          <el-menu-item index="/system/permission">权限管理</el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-aside>

    <!-- 主内容区 -->
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="header-left">
          <el-button
            type="text"
            @click="toggleCollapse"
            class="collapse-btn"
          >
            <el-icon><Fold v-if="!isCollapse" /><Expand v-else /></el-icon>
          </el-button>
          
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
              v-for="item in breadcrumbs"
              :key="item.path"
              :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-avatar :size="32" :src="userStore.userInfo?.avatar">
                {{ userStore.userInfo?.nickname?.charAt(0) }}
              </el-avatar>
              <span class="username">{{ userStore.userInfo?.nickname }}</span>
              <el-icon><ArrowDown /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <!-- 主内容 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import {
  Odometer as Dashboard,
  User,
  Calendar,
  OfficeBuilding,
  ChatDotRound as Message,
  Setting,
  Fold,
  Expand,
  ArrowDown,
  CaretRight as Parking,
  SuitcaseLine as Service
} from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const isCollapse = ref(false)

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 默认展开的子菜单
const defaultOpeneds = computed(() => {
  const path = route.path
  if (path.startsWith('/user/')) {
    return ['user']
  } else if (path.startsWith('/appointment/')) {
    return ['appointment']
  } else if (path.startsWith('/cemetery/') || path.startsWith('/gravesite/')) {
    return ['cemetery']
  } else if (path.startsWith('/parking/')) {
    return ['parking']
  } else if (path.startsWith('/message/')) {
    return ['message']
  } else if (path.startsWith('/system/')) {
    return ['system']
  }
  return []
})

// 面包屑导航
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta?.title)
  return matched.map(item => ({
    path: item.path,
    title: item.meta?.title
  }))
})

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}

// 处理用户下拉菜单命令
const handleCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      // 跳转到个人设置页面
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        userStore.logout()
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}
</script>

<style scoped>
.layout-container {
  height: 100vh;
}

.sidebar {
  background-color: #304156;
  transition: width 0.3s;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  color: white;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #434a50;
}

.logo-icon {
  width: 32px;
  height: 32px;
  margin-right: 8px;
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.sidebar-menu {
  border: none;
  background-color: #304156 !important;
  height: calc(100vh - 60px);
}

.sidebar-menu :deep(.el-menu-item),
.sidebar-menu :deep(.el-sub-menu__title) {
  color: #bfcbd9 !important;
  background-color: transparent !important;
}

.sidebar-menu :deep(.el-menu-item:hover),
.sidebar-menu :deep(.el-sub-menu__title:hover) {
  background-color: #434a50 !important;
  color: #ffffff !important;
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background-color: #409eff !important;
  color: white !important;
}

.sidebar-menu :deep(.el-sub-menu .el-menu) {
  background-color: #263445 !important;
}

.sidebar-menu :deep(.el-sub-menu .el-menu .el-menu-item) {
  background-color: transparent !important;
  color: #bfcbd9 !important;
}

.sidebar-menu :deep(.el-sub-menu .el-menu .el-menu-item:hover) {
  background-color: #434a50 !important;
  color: #ffffff !important;
}

.sidebar-menu :deep(.el-sub-menu .el-menu .el-menu-item.is-active) {
  background-color: #409eff !important;
  color: white !important;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  margin-right: 20px;
  font-size: 18px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.username {
  margin: 0 8px;
  font-size: 14px;
}

.main-content {
  background-color: #f0f2f5;
  padding: 20px;
}
</style>
