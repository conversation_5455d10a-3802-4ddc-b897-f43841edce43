import request from '@/utils/request'

// 代客服务API
export const proxyServiceApi = {
  // ==================== 套餐管理 ====================
  
  /**
   * 获取套餐分页列表
   */
  getPackages(params) {
    return request({
      url: '/admin/proxy/packages',
      method: 'get',
      params
    })
  },

  /**
   * 获取套餐详情
   */
  getPackageDetail(id) {
    return request({
      url: `/admin/proxy/packages/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建套餐
   */
  createPackage(data) {
    return request({
      url: '/admin/proxy/packages',
      method: 'post',
      data
    })
  },

  /**
   * 更新套餐
   */
  updatePackage(id, data) {
    return request({
      url: `/admin/proxy/packages/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除套餐
   */
  deletePackage(id) {
    return request({
      url: `/admin/proxy/packages/${id}`,
      method: 'delete'
    })
  },

  // ==================== 服务人员管理 ====================

  /**
   * 获取服务人员分页列表
   */
  getStaff(params) {
    return request({
      url: '/admin/proxy/staff',
      method: 'get',
      params
    })
  },

  /**
   * 获取活跃服务人员列表
   */
  getActiveStaff() {
    return request({
      url: '/admin/proxy/staff/active',
      method: 'get'
    })
  },

  /**
   * 获取服务人员详情
   */
  getStaffDetail(id) {
    return request({
      url: `/admin/proxy/staff/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建服务人员
   */
  createStaff(data) {
    return request({
      url: '/admin/proxy/staff',
      method: 'post',
      data
    })
  },

  /**
   * 更新服务人员
   */
  updateStaff(id, data) {
    return request({
      url: `/admin/proxy/staff/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除服务人员
   */
  deleteStaff(id) {
    return request({
      url: `/admin/proxy/staff/${id}`,
      method: 'delete'
    })
  },

  // ==================== 订单管理 ====================

  /**
   * 获取订单分页列表
   */
  getOrders(params) {
    return request({
      url: '/admin/proxy/orders',
      method: 'get',
      params
    })
  },

  /**
   * 获取订单详情
   */
  getOrderDetail(id) {
    return request({
      url: `/admin/proxy/orders/${id}`,
      method: 'get'
    })
  },

  /**
   * 更新订单状态
   */
  updateOrderStatus(id, status) {
    return request({
      url: `/admin/proxy/orders/${id}/status`,
      method: 'put',
      params: { status }
    })
  },

  /**
   * 分配服务人员
   */
  assignStaff(orderId, staffId) {
    return request({
      url: `/admin/proxy/orders/${orderId}/assign/${staffId}`,
      method: 'put'
    })
  },

  /**
   * 获取订单统计信息
   */
  getOrderStatistics(params) {
    return request({
      url: '/admin/proxy/orders/statistics',
      method: 'get',
      params
    })
  },

  // ==================== 服务报告管理 ====================

  /**
   * 创建服务报告
   */
  createServiceReport(data) {
    return request({
      url: '/admin/proxy/reports',
      method: 'post',
      data
    })
  },

  /**
   * 获取服务报告
   */
  getServiceReport(orderId) {
    return request({
      url: `/admin/proxy/reports/order/${orderId}`,
      method: 'get'
    })
  },

  /**
   * 更新服务报告
   */
  updateServiceReport(id, data) {
    return request({
      url: `/admin/proxy/reports/${id}`,
      method: 'put',
      data
    })
  }
}

// 小程序端API
export const proxyServiceClientApi = {
  /**
   * 获取服务套餐列表
   */
  getPackages() {
    return request({
      url: '/proxy/packages',
      method: 'get'
    })
  },

  /**
   * 获取套餐详情
   */
  getPackageDetail(id) {
    return request({
      url: `/proxy/package/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建代客订单
   */
  createOrder(data) {
    return request({
      url: '/proxy/order',
      method: 'post',
      data
    })
  },

  /**
   * 获取用户订单列表
   */
  getOrders() {
    return request({
      url: '/proxy/orders',
      method: 'get'
    })
  },

  /**
   * 获取订单详情
   */
  getOrderDetail(id) {
    return request({
      url: `/proxy/order/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取服务报告
   */
  getServiceReport(orderId) {
    return request({
      url: `/proxy/report/${orderId}`,
      method: 'get'
    })
  },

  /**
   * 获取订单照片
   */
  getOrderPhotos(orderId) {
    return request({
      url: `/proxy/order/${orderId}/photos`,
      method: 'get'
    })
  },

  /**
   * 评价服务
   */
  rateService(orderId, data) {
    return request({
      url: `/proxy/order/${orderId}/rate`,
      method: 'post',
      data
    })
  },

  /**
   * 获取订单评价
   */
  getOrderRating(orderId) {
    return request({
      url: `/proxy/order/${orderId}/rating`,
      method: 'get'
    })
  },

  /**
   * 取消订单
   */
  cancelOrder(id) {
    return request({
      url: `/proxy/order/${id}`,
      method: 'delete'
    })
  }
}
