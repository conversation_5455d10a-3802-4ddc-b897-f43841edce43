import request from './request'

// 管理员登录请求接口
export interface AdminLoginRequest {
  username: string
  password: string
  captcha?: string
  captchaKey?: string
}

// 管理员登录响应接口
export interface AdminLoginResponse {
  token: string
  adminId: number
  adminInfo: AdminInfo
  expiration: number
}

// 管理员信息接口
export interface AdminInfo {
  id: number
  username: string
  nickname: string
  email?: string
  phone?: string
  avatarUrl?: string
  status: string
  lastLoginTime?: string
  lastLoginIp?: string
  loginCount: number
  createdAt: string
  updatedAt: string
  roles: Role[]
  permissions: Permission[]
  menus: Permission[]
  roleCodes: string[]
  permissionCodes: string[]
}

// 角色接口
export interface Role {
  id: number
  roleCode: string
  roleName: string
  description?: string
  status: string
  sortOrder: number
  createdAt: string
  updatedAt: string
  permissions?: Permission[]
  permissionIds?: number[]
  adminCount?: number
}

// 权限接口
export interface Permission {
  id: number
  permissionCode: string
  permissionName: string
  permissionType: 'menu' | 'button' | 'api'
  parentId: number
  path?: string
  component?: string
  icon?: string
  sortOrder: number
  status: string
  description?: string
  createdAt: string
  updatedAt: string
  children?: Permission[]
  checked?: boolean
}

// 管理员创建/更新请求接口
export interface AdminRequest {
  id?: number
  username: string
  password?: string
  nickname: string
  email?: string
  phone?: string
  avatarUrl?: string
  status: string
  roleIds: number[]
}

// 分页查询参数接口
export interface AdminPageParams {
  current: number
  size: number
  keyword?: string
  status?: string
}

// 分页响应接口
export interface PageResponse<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 管理员API
 */
export const adminAPI = {
  // 管理员登录
  login: (data: AdminLoginRequest) => {
    return request.post<AdminLoginResponse>('/admin/login', data)
  },

  // 获取当前管理员信息
  getCurrentInfo: () => {
    return request.get<AdminInfo>('/admin/info')
  },

  // 管理员登出
  logout: () => {
    return request.post<string>('/admin/logout')
  },

  // 分页查询管理员列表
  getAdminList: (params: AdminPageParams) => {
    return request.get<PageResponse<AdminInfo>>('/admin/list', { params })
  },

  // 根据ID获取管理员详情
  getAdminById: (id: number) => {
    return request.get<AdminInfo>(`/admin/${id}`)
  },

  // 创建管理员
  createAdmin: (data: AdminRequest) => {
    return request.post<string>('/admin', data)
  },

  // 更新管理员
  updateAdmin: (id: number, data: AdminRequest) => {
    return request.put<string>(`/admin/${id}`, data)
  },

  // 删除管理员
  deleteAdmin: (id: number) => {
    return request.delete<string>(`/admin/${id}`)
  },

  // 重置管理员密码
  resetPassword: (id: number, newPassword: string) => {
    return request.post<string>(`/admin/${id}/reset-password`, { newPassword })
  },

  // 更新管理员状态
  updateStatus: (id: number, status: string) => {
    return request.post<string>(`/admin/${id}/status`, { status })
  },

  // 获取管理员角色列表
  getAdminRoles: (id: number) => {
    return request.get<Role[]>(`/admin/${id}/roles`)
  },

  // 获取管理员权限列表
  getAdminPermissions: (id: number) => {
    return request.get<Permission[]>(`/admin/${id}/permissions`)
  },

  // 获取管理员菜单权限
  getAdminMenus: (id: number) => {
    return request.get<Permission[]>(`/admin/${id}/menus`)
  },

  // 获取当前管理员菜单权限
  getCurrentMenus: () => {
    return request.get<Permission[]>('/admin/menus')
  }
}

/**
 * 角色API
 */
export const roleAPI = {
  // 分页查询角色列表
  getRoleList: (params: { current: number; size: number; keyword?: string; status?: string }) => {
    return request.get<PageResponse<Role>>('/role/list', { params })
  },

  // 根据ID获取角色详情
  getRoleById: (id: number) => {
    return request.get<Role>(`/role/${id}`)
  },

  // 创建角色
  createRole: (data: Partial<Role>) => {
    return request.post<string>('/role', data)
  },

  // 更新角色
  updateRole: (id: number, data: Partial<Role>) => {
    return request.put<string>(`/role/${id}`, data)
  },

  // 删除角色
  deleteRole: (id: number) => {
    return request.delete<string>(`/role/${id}`)
  },

  // 获取所有活跃角色
  getActiveRoles: () => {
    return request.get<Role[]>('/role/active')
  },

  // 获取角色权限列表
  getRolePermissions: (id: number) => {
    return request.get<Permission[]>(`/role/${id}/permissions`)
  },

  // 分配角色权限
  assignPermissions: (id: number, permissionIds: number[]) => {
    return request.post<string>(`/role/${id}/permissions`, { permissionIds })
  }
}

/**
 * 权限API
 */
export const permissionAPI = {
  // 分页查询权限列表
  getPermissionList: (params: { 
    current: number; 
    size: number; 
    keyword?: string; 
    permissionType?: string; 
    status?: string 
  }) => {
    return request.get<PageResponse<Permission>>('/permission/list', { params })
  },

  // 根据ID获取权限详情
  getPermissionById: (id: number) => {
    return request.get<Permission>(`/permission/${id}`)
  },

  // 创建权限
  createPermission: (data: Partial<Permission>) => {
    return request.post<string>('/permission', data)
  },

  // 更新权限
  updatePermission: (id: number, data: Partial<Permission>) => {
    return request.put<string>(`/permission/${id}`, data)
  },

  // 删除权限
  deletePermission: (id: number) => {
    return request.delete<string>(`/permission/${id}`)
  },

  // 获取所有活跃权限
  getActivePermissions: () => {
    return request.get<Permission[]>('/permission/active')
  },

  // 获取菜单权限树
  getMenuPermissions: () => {
    return request.get<Permission[]>('/permission/menu-tree')
  },

  // 获取权限树
  getPermissionTree: () => {
    return request.get<Permission[]>('/permission/tree')
  }
}
