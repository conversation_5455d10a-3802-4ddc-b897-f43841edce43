import { createRouter, createWebHistory } from 'vue-router'
import { isLoggedIn } from '@/utils/auth'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'Layout',
      component: () => import('@/layout/index.vue'),
      redirect: '/dashboard',
      meta: { requiresAuth: true },
      children: [
        {
          path: '/dashboard',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue'),
          meta: { title: '仪表盘', icon: 'dashboard' }
        },
        {
          path: '/appointment/list',
          name: 'AppointmentList',
          component: () => import('@/views/appointment/List.vue'),
          meta: { title: '预约列表', icon: 'calendar' }
        },
        {
          path: '/appointment/calendar',
          name: 'AppointmentCalendar',
          component: () => import('@/views/appointment/Calendar.vue'),
          meta: { title: '预约日历', icon: 'calendar' }
        },
        {
          path: '/appointment/stats',
          name: 'AppointmentStats',
          component: () => import('@/views/appointment/Stats.vue'),
          meta: { title: '预约统计', icon: 'calendar' }
        },
        {
          path: '/cemetery/list',
          name: 'CemeteryList',
          component: () => import('@/views/cemetery/List.vue'),
          meta: { title: '墓园管理', icon: 'office-building' }
        },
        {
          path: '/gravesite/list',
          name: 'GraveSiteList',
          component: () => import('@/views/gravesite/List.vue'),
          meta: { title: '墓位管理', icon: 'grid' }
        },
        // 停车场管理路由
        {
          path: '/parking/lots',
          name: 'ParkingLots',
          component: () => import('@/views/parking/lots/index.vue'),
          meta: { title: '停车场管理', icon: 'parking' }
        },
        {
          path: '/parking/lots/:id',
          name: 'ParkingLotDetail',
          component: () => import('@/views/parking/lots/detail.vue'),
          meta: { title: '停车场详情', icon: 'parking' }
        },
        {
          path: '/parking/spaces',
          name: 'ParkingSpaces',
          component: () => import('@/views/parking/spaces/index.vue'),
          meta: { title: '停车位管理', icon: 'grid' }
        },
        {
          path: '/parking/bookings',
          name: 'ParkingBookings',
          component: () => import('@/views/parking/bookings/index.vue'),
          meta: { title: '停车预约记录', icon: 'document' }
        },
        // 代客服务管理路由
        {
          path: '/proxy-service',
          name: 'ProxyService',
          component: () => import('@/views/proxy-service/index.vue'),
          meta: { title: '代客服务管理', icon: 'service' }
        },
        {
          path: '/test/api',
          name: 'ApiTest',
          component: () => import('@/views/test/ApiTest.vue'),
          meta: { title: 'API测试', icon: 'setting' }
        },
        {
          path: '/message/list',
          name: 'MessageList',
          component: () => import('@/views/message/List.vue'),
          meta: { title: '消息列表', icon: 'message' }
        },
        {
          path: '/message/send',
          name: 'MessageSend',
          component: () => import('@/views/message/Send.vue'),
          meta: { title: '发送消息', icon: 'message' }
        },
        {
          path: '/system/admin',
          name: 'SystemAdmin',
          component: () => import('@/views/system/Admin.vue'),
          meta: { title: '管理员管理', icon: 'user' }
        },
        {
          path: '/system/role',
          name: 'SystemRole',
          component: () => import('@/views/system/Role.vue'),
          meta: { title: '角色管理', icon: 'user-filled' }
        },
        {
          path: '/system/permission',
          name: 'SystemPermission',
          component: () => import('@/views/system/Permission.vue'),
          meta: { title: '权限管理', icon: 'key' }
        },
        {
          path: '/user/list',
          name: 'UserList',
          component: () => import('@/views/user/List.vue'),
          meta: { title: '用户管理', icon: 'user' }
        },
        {
          path: '/test',
          name: 'Test',
          component: () => import('@/views/Test.vue'),
          meta: { title: '登录测试', requiresAuth: false }
        },
        {
          path: '/test/auth',
          name: 'AuthTest',
          component: () => import('@/views/test/AuthTest.vue'),
          meta: { title: 'JWT认证测试', icon: 'key' }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 使用认证工具检查登录状态（包含token有效性验证）
  const userLoggedIn = isLoggedIn()

  console.log('路由守卫:', {
    to: to.path,
    isLoggedIn: userLoggedIn,
    requiresAuth: to.meta.requiresAuth
  })

  if (to.meta.requiresAuth !== false && !userLoggedIn) {
    console.log('未登录或token已过期，跳转到登录页')
    next('/login')
  } else if (to.path === '/login' && userLoggedIn) {
    console.log('已登录，跳转到首页')
    next('/')
  } else {
    console.log('正常跳转')
    next()
  }
})

export default router
